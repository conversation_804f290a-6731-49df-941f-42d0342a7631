// Angular core and common modules
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// Third-party modules
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';

// Configuration and base classes
import { BaseEditComponent } from '../../../../../../config/base.edit.component';
import { Constant } from '../../../../../../config/constants';

// Models
import { QuotationCargoDetail } from '../../../../../../models/quotation/quotation-cargo-details';

// Services
import { LoadingService } from '../../../../../../services/loading.service';
import { AuthService } from '../../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../../shared/services/common.service';
import { ToastService } from '../../../../../../shared/services/toast.service';

// Shared and custom components
import { ValidationMessageComponent } from '../../../../../../shared/common-component/validation-message/validation-message.component';

// Shared directives
import { AllowNumberOnlyDirective } from '../../../../../../shared/directives/allow-number-only.directive';
import { CurrencyFormatterDirective } from '../../../../../../shared/directives/custom-currency.directive';

// Managers
import { RestResponse } from '../../../../../../models/common/auth.model';
import { QuotationCargoManager } from '../quotation-cargo.manager';

@Component({
    selector: 'app-edit-quotation-cargo-details',
    standalone: true,
    imports: [
        AllowNumberOnlyDirective,
        CommonModule,
        CurrencyFormatterDirective,
        FormsModule,
        NgSelectModule,
        NgxMaskDirective,
        TranslateModule,
        ValidationMessageComponent,
    ],
    templateUrl: './edit-quotation-cargo-details.component.html',
    styleUrl: './edit-quotation-cargo-details.component.scss',
    providers: [provideNgxMask()],
})
export class EditQuotationCargoDetailsComponent extends BaseEditComponent {
    @Input() modalRef!: NgbModalRef;
    @Input() quotationCargoDetail!: QuotationCargoDetail;
    @Input() isOpenedInModal!: boolean;
    @Input() rateSheet!: string | null;

    @Output() closeEvent = new EventEmitter<string>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    cargoTypeOptions = Constant.CARGO_TYPE_OPTIONS;
    rateTypes = Constant.RATE_TYPES;
    weightTypeOptions = Constant.WEIGHT_TYPE_OPTIONS;

    freightAmount: any;

    constructor(
        public override toastService: ToastService,
        protected authService: AuthService,
        protected override commonService: CommonService,
        protected override loadingService: LoadingService,
        protected override route: ActivatedRoute,
        protected override router: Router,
        protected override translateService: TranslateService,
        protected quotationCargoManager: QuotationCargoManager,
    ) {
        super(quotationCargoManager, commonService, toastService, loadingService, route, router, translateService);
    }

    ngOnInit() {
        if (!this.quotationCargoDetail?.id) {
            this.quotationCargoDetail = new QuotationCargoDetail();
            this.setId();
            this.setRecord(this.quotationCargoDetail);
            this.request.isNewRecord = true;
        } else {
            this.request.isNewRecord = false;
            this.quotationCargoDetail = QuotationCargoDetail.fromResponse(this.quotationCargoDetail);
            const qty = this.quotationCargoDetail.quantity || 0;
            this.freightAmount = {
                freight: qty > 0 ? Number((this.quotationCargoDetail.freight / qty).toFixed(2)) : 0,
                fuelCharges: qty > 0 ? Number((this.quotationCargoDetail.fuelCharges / qty).toFixed(2)) : 0,
                gst: qty > 0 ? Number((this.quotationCargoDetail.gst / qty).toFixed(2)) : 0,
                total: qty > 0 ? Number((this.quotationCargoDetail.total / qty).toFixed(2)) : 0,
            };
            this.setId();
            this.setRecord(this.quotationCargoDetail);
        }
    }

    getVolume() {
        const { width = 0, height = 0, length = 0 } = this.quotationCargoDetail;
        const volume = length * width * height;
        this.quotationCargoDetail.volume = parseFloat(volume.toFixed(2));
    }

    onQuantityChange() {
        const { freight = 0, fuelCharges = 0, gst = 0, total = 0 } = this.freightAmount;
        const quantity = this.quotationCargoDetail?.quantity ?? 0;
        Object.assign(this.quotationCargoDetail, {
            freight: parseFloat((freight * quantity).toFixed(2)),
            fuelCharges: parseFloat((fuelCharges * quantity).toFixed(2)),
            gst: parseFloat((gst * quantity).toFixed(2)),
            total: parseFloat((total * quantity).toFixed(2)),
        });
    }

    setWeightToLbs() {
        const { weight, weightType } = this.quotationCargoDetail;
        if (weight == null || undefined) {   // covers both null and undefined
            return;
        }

        this.quotationCargoDetail.weightInPounds =
            weightType?.toUpperCase() === 'LBS' ? weight : this.quotationCargoDetail.weight * 2.20462;
    }

    override onSaveSuccess(data: any) {
        if (this.isOpenedInModal) {
            this.saveButtonClicked.emit();
            this.modalRef?.close();
        }
    }

    getFreightValues() {
        const filter = this.filterParam.filtering;

        if (this.rateSheet) {
            filter.rateSheetId = this.rateSheet;
        }

        filter.rateType = this.quotationCargoDetail.rateType ?? null;
        filter.weight = this.quotationCargoDetail.weightInPounds ?? null;

        if (!filter.rateType && !filter.weight) {
            return;
        }

        this.quotationCargoManager.getFreightAmount(this.filterParam).then((response: RestResponse) => {
            this.freightAmount = response;

            const { freight = 0, fuelCharges = 0, gst = 0, total = 0 } = this.freightAmount;

            const quantity = this.quotationCargoDetail?.quantity ?? 0;

            Object.assign(this.quotationCargoDetail, {
                freight: parseFloat((freight * quantity).toFixed(2)),
                fuelCharges: parseFloat((fuelCharges * quantity).toFixed(2)),
                gst: parseFloat((gst * quantity).toFixed(2)),
                total: parseFloat((total * quantity).toFixed(2)),
            });
        });
    }

    generateTotalAmount() {
        const { freight = 0, fuelCharges = 0, gst = 0 } = this.quotationCargoDetail;
        const quantity = this.quotationCargoDetail?.quantity ?? 0;

        const total = (freight + fuelCharges + gst);
        this.quotationCargoDetail.total = parseFloat(total.toFixed(2));
    }

    private setId() {
        this.quotationCargoDetail.quotation = this.route.snapshot.paramMap.get('id') as string;
    }
}

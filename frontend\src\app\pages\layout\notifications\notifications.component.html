<div class="site-main-container container">
    <div
        #scrollContainer
        class="notification-scroll-wrapper scrollable-area"
        [ngClass]="notifications.length === 0 ? 'scrollable-reduced' : 'scrollable-full'"
    >
        <div class="notification-container" [ngClass]="{ 'h-100': notifications.length === 0 }">
            <!-- Empty State -->
            <div *ngIf="isLoaded && notifications.length === 0; else notificationsList" class="no-notifications">
                <img
                    src="../../../../assets/images/svg/no-data.svg"
                    alt="No notifications"
                    class="no-notifications-img"
                />
                <h3>No Notifications available</h3>
                <p>You’re all caught up! 🎉</p>
            </div>

            <!-- Notification List -->
            <ng-template #notificationsList>
                <div class="notifications-header d-flex gap-2 mb-2 justify-content-end">
                    <button class="notification-btn read-btn" (click)="markAllAsRead()" *ngIf="hasUnreadNotifications">
                        <i class="bi bi-check-all font-size-20"></i>
                        {{ 'Notification.ReadAll' | translate }}
                    </button>

                    <button class="notification-btn clear-btn" (click)="clearAll()" *ngIf="hasNonDeletedNotifications">
                        <i class="bi bi-check-all font-size-20"></i>
                        {{ 'Notification.clearAll' | translate }}
                    </button>
                </div>

                <div class="notification-group" *ngFor="let group of groupedNotifications">
                    <h6 class="group-title">{{ group.label }}</h6>

                    <div
                        class="notification-card d-md-flex"
                        *ngFor="let item of group.items"
                        [ngClass]="[!item.isRead ? 'unread' : 'read']"
                    >
                        <div class="d-none d-md-block mt-2">
                            <img
                                [src]="getIconClass(item.entityName)"
                                [alt]="getIconClass(item.entityName)"
                                width="25"
                                height="25"
                            />
                        </div>

                        <div
                            class="w-100 cursor-pointer"
                            (click)="item.status !== 'DELETED' ? redirectToUser(item) : null"
                        >
                            <div class="notification-header">
                                <span class="me-2 d-block d-md-none">
                                    <img [src]="getIconClass(item.entityName)" [alt]="getIconClass(item.entityName)" />
                                </span>
                                <span class="title">{{ item.title }}</span>
                            </div>

                            <div
                                class="d-flex flex-column flex-lg-row justify-content-lg-between align-items-lg-center message-container"
                            >
                                <span class="ms-5 d-lg-none"></span>
                                <div
                                    class="notification-message mt-2 mt-md-0"
                                    [innerHTML]="formatMessage(item.message)"
                                ></div>

                                <div class="actions justify-content-end">
                                    <button
                                        class="read"
                                        *ngIf="!item.isRead"
                                        (click)="markAsRead(item, $event)"
                                        [ngbTooltip]="'Mark as Read'"
                                    >
                                        <i class="bi bi-envelope-open"></i>
                                    </button>
                                    <button
                                        class="delete"
                                        (click)="deleteNotification(item, $event)"
                                        [ngbTooltip]="'Clear Notification'"
                                    >
                                        <i class="bi bi-x-circle"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loader -->
                <div *ngIf="!isLoaded" class="display-flex-center h-100">
                    <span class="loader"></span>
                </div>

                <!-- Sentinel -->
                <div #sentinel></div>
            </ng-template>
        </div>
    </div>
</div>

<div class="site-main-container">
    <div class="position-relative">
        <div class="dashboard-main-filter-section px-0">
            <div class="custom-input-group custom-search-bar-outer mb-sm-0">
                <input class="form-control search-form-control custom-search-bar"
                    placeholder="Search By Driver and vehicle..." appDelayedInput #searchInput
                    (delayedInput)="search($event)" [delayTime]="1000">
                <i class="bi bi-search"></i>
            </div>

            <!-- Buttons -->
            <div class="d-flex flex-sm-row gap-2">
                <!-- Filter Button -->
                <!-- <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect>
                    <span>
                        <img src="/assets/images/icons/Filter_icon.svg" alt="filter-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline fw-medium ms-1 me-3 custom-text-button">{{"COMMON.FILTER" |
                        translate}}
                    </span>
                </button> -->

                <!-- Export to Excel Button  -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    [class.d-none]="records.length < 1" (click)="exportToExcel(filterParam)">
                    <span class="text-center">
                        <img src="/assets/images/icons/export.svg" alt="export-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{"UserPermission.export" |
                        translate}}</span>
                </button>
            </div>
        </div>

        <!-- Accordion -->
    </div>

    <!-- Table Container -->
    <div class="col-md-12">
        <!-- Table Layout -->
        <div class="site-table-container">
            <div class="table-responsive" [ngClass]="{ 'has-records': fuelReceipt
            .length > 0 }">
                <table class="table table-hover site-table-layout" datatable [dtOptions]="dtOptions"
                    [dtTrigger]="dtTrigger">
                    <thead class="table-head">
                        <tr>
                            <th>{{'FuelReceipt.driverName' | translate }}</th>
                            <th>{{'FuelReceipt.vehicle' | translate}} </th>
                            <th>{{'FuelReceipt.meterReading' | translate }}{{'COMMON.inKm' | translate}}</th>
                            <th>{{'FuelReceipt.fuelInLiters' | translate }}{{'COMMON.inLtr'| translate}}</th>
                            <th>{{'FuelReceipt.fuelCost' | translate }}{{'COMMON.inDollar' | translate}}</th>
                            <th>{{'FuelReceipt.fuelType' | translate}}</th>
                            <th>{{'COMMON.CREATED_ON' | translate}}</th>
                            <th>{{'COMMON.ACTION' | translate}}</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>

        <!-- Table Body -->
        <div class="table-container-body">
            <ng-template #driver let-data="adtData">
                <span>{{ data.driverDetail.fullName
                    }}</span>
            </ng-template>

            <ng-template #vehicle let-data="adtData">
                <span>{{ data.vehicleDetail.name }}</span>
            </ng-template>

            <ng-template #meterReading let-data="adtData">
                <span class="text-center">{{ data.meterReading }}</span>
            </ng-template>

            <ng-template #fuelInLiters let-data="adtData">
                <span>{{ data.fuelInLiters }}</span>
            </ng-template>

            <ng-template #fuelCost let-data="adtData">
                <span>{{ data.fuelCost }}</span>
            </ng-template>

            <ng-template #fuelType let-data="adtData">
                <div class="d-flex">
                    <span [appStatusBadge]="data.fuelType | removeUnderscore"></span>
                </div>
            </ng-template>

            <ng-template #createdOn let-data="adtData">
                <span>{{ data.createdOn | dateFormat }} </span>
            </ng-template>
            <ng-template #action let-data="adtData">
                <div class="action-icons">
                    <button class="delete-btn " (click)="remove(data.id,resourceType)">
                        <img width="22" src="/assets/images/icons/delete-icon.svg" alt="delete" />
                    </button>
                </div>
            </ng-template>
        </div>
    </div>
</div>
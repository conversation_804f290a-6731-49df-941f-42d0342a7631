<div class="notification-list">
    <ng-container *ngFor="let group of groupedNotifications">
        <!-- Sticky header for each date section -->
        <div class="sticky-header">{{ group.label }}</div>

        <!-- Notifications under this group -->
        <div class="notification-item" *ngFor="let notification of group.items">
            <div class="avatar placeholder">
                <i class="bi bi-chat-text"></i>
            </div>
            <div class="content" appRippleEffect>
                <p class="title ellipsis-1-line" [ngbTooltip]="notification.title" container="body" placement="auto">
                    {{ notification.title }}
                </p>
                <span class="time">{{ notification.time }}</span>
            </div>
            <span class="dot" *ngIf="!notification.isRead"></span>
        </div>
    </ng-container>
</div>
<div>
    <button class="view-all" appRippleEffect [routerLink]="['/dashboard/notifications']">
        {{'Notification.viewAllNotifications' | translate}}
        <span *ngIf="count > 0" class="badge">({{ count }})</span>
    </button>
</div>
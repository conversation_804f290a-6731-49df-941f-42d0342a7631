// -----------------------------------
// Font Sizes
// -----------------------------------
$font-size-12: 12px;
$font-size-13: 13px;
$font-size-14: 14px;
$font-size-16: 16px;

// -----------------------------------
// Font Weights
// -----------------------------------
$font-weight-400: 400;
$font-weight-500: 500;
$font-weight-600: 600;
$font-weight-700: 700;

// -----------------------------------
// Cursor Styles
// -----------------------------------
$cursor-pointer: pointer;
$cursor-default: default;
$cursor-disabled: not-allowed;

// -----------------------------------
// Border Radius
// -----------------------------------
$border-radius-12: 12px;

// -----------------------------------
// Global Colors
// -----------------------------------
$white-color: #fff;
$white-smoke-color: #f5f5f5;
$black-color: #000000;
$light-hover-color: #fbe07b;
$card-background-color: #ffffffd1;
$form-border-color: #dc3545;
$disable-color: #f9f9f9;

$scroll-bar-color: #f5f5f5;
$scroll-bar-box-shadow: inset 0 0 6px $white-color;

// -----------------------------------
// Typography
// -----------------------------------
$body-font-family: 'Montserrat', sans-serif;
$body-color: #000000;

// -----------------------------------
// UI Elements
// -----------------------------------
$tab-background: #efefef;
$active-tab-background: #f6bf0b;
$active-tab-container-background: #f6bf0b14;
$input-border: #f0f4f9;
$light-bg-color: #f1f3f7;
$light-grey: #e3e3e3;
$filter-border-color: #00000029;

// -----------------------------------
// Theme Constants (Default)
$theme-color: #9b4337;
$theme-secondary-color: #9b4337;
$theme-btn-bg-color: $theme-color;
$theme-btn-bg-hover-color: #000000;

// -----------------------------------
// Status Colors Map
// -----------------------------------
$status-colors: (
    'active': #66c61c,
    'inactive': #ff4405,
    'admin': #0ba5ec,
    'driver': #d92d20,
    'customer': #16b364,
    'weight': #007bff,
    'volume': #17a2b8,
    'fullskid': #28a745,
    'halfskid': #ffc107,
    'rush': #ff6b6b,
    'flatdeck': #1e90ff,
    'trucktrailer': #ffa500,
    'regular': #16b364,

    // SHIPMENT_STATUS_OPTIONS
    'new': #ec0b0b,
    'assigned': #007bff,
    'intransit': #ffc107,
    'delivered': #28a745,
    'completed': #6f42c1,

    // SHIPMENT_PAYMENT_TYPE_OPTIONS
    'prepaid': #6f42c1,
    'collect': #20c997,

    // SHIPMENT_PAYMENT_STATUS_OPTIONS
    'pending': #fd7e14,
    'invoiced': #198754,

    // QUOTATION_STATUS_OPTIONS
    'requested': #0ba5ec,
    'clientapproval': #ffc107,
    'confirmed': #28a745,
    'rejected': #ec0b0b,

    // Barcode Status
    'unused': #20c997,
    'used': #ec0b0b,
    'printed': #007bff,
    'notprinted': #fd7e14,

    // Contact us Status
    'inprogress': #ffc107,
    'cancelled': #ec0b0b,

    // Fuel Receipt
    'petrol':#005F6A,
    'diesel':#fd7e14,
    'gasoline':#ff6b6b,
    'electric':#28a745,
    'propane':#6f42c1,
    'naturalgas':#66c61c,
    'ethanol':#ec0b0b,
    'flexfuel':#007bff,
    'hydrogen':#ffc107,
);

// -----------------------------------
// Theme Map: US & Canada
// -----------------------------------
$themes: (
    'us': (primary: #b6b5b4,
        secondary: #4d4b4d,
        ternary: #4d4b4d,
        quaternary: #fa814d,
        uploader-background: #edecec,
        uploader-dashed: null,
        button-pre-active: null,
        login-color: null,
    ),
    'canada': (primary: #fcd616,
        secondary: #ed5a00,
        ternary: #fa814d,
        quaternary: null,
        uploader-background: #fff8d275,
        uploader-dashed: #fcd616,
        button-pre-active: #fff8d2,
        login-color: #ffea00,
    ),
);

// -----------------------------------
// Auth Theme Colors (Global)
$auth-theme: (
    primary: #ed5a00,
    secondary: #fa814d,
    text: #333132,
);

// -----------------------------------
// Functions to Get Theme Values
// -----------------------------------
@function theme-color($region, $key) {
    @return map-get(map-get($themes, $region), $key);
}

// Example: theme-color('us', 'primary')
// Result: #b6b5b4

// -----------------------------------
// Theme Mixins (for setting CSS variables)
// -----------------------------------
@mixin apply-theme($region) {
    --primary-color: #{theme-color($region, 'primary')};
    --secondary-color: #{theme-color($region, 'secondary')};
    --ternary-color: #{theme-color($region, 'ternary')};
    --uploader-background-color: #{theme-color($region, 'uploader-background')};

    @if theme-color($region, 'uploader-dashed') {
        --uploader-dashed-color: #{theme-color($region, 'uploader-dashed')};
    }

    @if theme-color($region, 'button-pre-active') {
        --button-pre-active-color: #{theme-color($region, 'button-pre-active')};
    }

    @if theme-color($region, 'login-color') {
        --login-color: #{theme-color($region, 'login-color')};
    }
}
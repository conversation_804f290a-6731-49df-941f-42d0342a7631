<div class="site-main-container">
    <div class="position-relative">
        <div class="dashboard-main-filter-section px-0">
            <div class="custom-input-group custom-search-bar-outer mb-sm-0">
                <input class="form-control search-form-control custom-search-bar" placeholder="Search..."
                    appDelayedInput (delayedInput)="search($event)" [delayTime]="1000" #searchInput>
                <i class="bi bi-search"></i>
            </div>

            <!-- Buttons -->
            <div class="d-flex flex-sm-row gap-2">
                <!-- Filter Button -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    (click)="accordion.toggle('filter');">
                    <span>
                        <img src="/assets/images/icons/Filter_icon.svg" alt="filter-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline fw-medium ms-1 me-3 custom-text-button">{{"COMMON.FILTER" |
                        translate}}
                    </span>
                </button>

                <!-- Export to Excel Button  -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    [class.d-none]="records.length < 1" (click)="exportToExcel(filterParam)">
                    <span class="text-center">
                        <img src="/assets/images/icons/export.svg" alt="export-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{"UserPermission.export" |
                        translate}}</span>
                </button>

                <!-- Add New Button  -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    [routerLink]="['/dashboard/rate-sheet/edit/0']">
                    <span class="text-center">
                        <img src="/assets/images/icons/Add_icon.svg" alt="Add-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{"COMMON.ADDNEW" |
                        translate}}</span>
                </button>
            </div>
        </div>

        <!-- Accordion -->
        <div ngbAccordion #accordion="ngbAccordion" class="mt-2">
            <div ngbAccordionItem="filter" class="border-0">
                <div ngbAccordionCollapse>
                    <div ngbAccordionBody class="filter-container p-4 w-100">
                        <ng-template>
                            <div class="row gx-0 gy-3">
                                <!-- Created on  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0">
                                    <app-ng-custom-date-range-picker ngDefaultControl name="selectDateRange"
                                        [labelName]="'RATE_SHEET.startDateEndDate' | translate"
                                        [inputFromDate]="filterParam.filtering.fromDate"
                                        [inputToDate]="filterParam.filtering.toDate"
                                        (setFromDate)="filterParam.filtering.fromDate = $event"
                                        (setToDate)="filterParam.filtering.toDate = $event"
                                        [customZIndex]="'z-index-3'"></app-ng-custom-date-range-picker>
                                </div>

                                <!-- Pickup Address  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="city" bindValue="city" name="StatusFilter"
                                            #StatusFilter="ngModel" [(ngModel)]="filterParam.filtering.pickUpCity"
                                            [items]="pickupCities" [typeahead]="searchPickupCitySubject"
                                            [loading]="loadingPickupCityNgSelect">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            "RATE_SHEET.pickupCity" | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!-- Delivery Address  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-0 ps-md-0 pe-xl-2">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="city" bindValue="city" name="StatusFilter"
                                            #StatusFilter="ngModel" [(ngModel)]="filterParam.filtering.deliveryCity"
                                            [typeahead]="searchDeliveryCitySubject" [items]="deliveryCities"
                                            [loading]="loadingDeliveryCityNgSelect">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            "RATE_SHEET.deliveryCity" | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!-- Duration Filter -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 pe-xl-0">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="name" bindValue="id" name="durationFilter"
                                            #DurationFilter="ngModel"
                                            [(ngModel)]="filterParam.filtering.createdWithinDays"
                                            [items]="durationFilter">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            "DashboardFilter.durationFilter" | translate
                                            }}</label>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end gap-2 mt-12">
                                    <button class="btn btn-primary custom-small-button" (click)="onApplyFilter()"
                                        appRippleEffect>{{"COMMON.APPLY" |
                                        translate}}</button>
                                    <button class="btn btn-primary custom-small-button"
                                        (click)="onClearFilter(searchInput)" appRippleEffect>{{"COMMON.CLEAR" |
                                        translate}}</button>
                                </div>
                            </div>
                        </ng-template>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Table Container -->
    <div class="col-md-12">
        <!-- Table Layout -->
        <div class="site-table-container">
            <div class="table-responsive" [ngClass]="{ 'has-records': rateSheets.length > 0 }">
                <table class="table table-hover site-table-layout" datatable [dtOptions]="dtOptions"
                    [dtTrigger]="dtTrigger">
                    <thead class="table-head">
                        <tr>
                            <th>{{ 'RATE_SHEET.objName#' | translate }}</th>
                            <th>{{ 'RATE_SHEET.pickupCity' | translate }}</th>
                            <th>{{ 'RATE_SHEET.deliveryCity' | translate }}</th>
                            <th>{{ 'RATE_SHEET.startDate' | translate }}</th>
                            <th>{{ 'RATE_SHEET.endDate' | translate }}</th>
                            <th>{{ 'COMMON.CREATED_ON' | translate }}</th>
                            <th class="th-action text-center">{{ 'COMMON.ACTION' | translate }}</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>

        <!-- Table Body -->
        <div class="table-container-body">
            <ng-template #name let-data="adtData">
                <a class="action-button text-capitalize ellipsis-2-line"
                    [routerLink]="['/dashboard/rate-sheet/edit/' + data.id]"
                    [state]="{ step:data?.rateSheetDetail?.step , firstLoad :true}" [appTooltipEllipsis]="data?.name"
                    ngbTooltip>
                    <strong>{{ data?.name }}</strong>
                </a>
            </ng-template>

            <ng-template #pickupAddress let-data="adtData">
                <span>{{ data.pickupAddressDetail?.city }}</span>
            </ng-template>

            <ng-template #deliveryAddress let-data="adtData">
                <span>{{ data.deliveryAddressDetail?.city }} </span>
            </ng-template>

            <ng-template #startDate let-data="adtData">
                <span>{{ data?.startDate | dateFormat }} </span>
            </ng-template>

            <ng-template #endDate let-data="adtData">
                <span>{{ data.endDate | dateFormat }} </span>
            </ng-template>

            <ng-template #createdOn let-data="adtData">
                <span>{{ data.createdOn | dateFormat }} </span>
            </ng-template>

            <ng-template #action let-data="adtData">
                <div class="action-icons d-flex justify-content-center">

                    <button class="edit-btn" [ngbPopover]="popContent"
                        popoverClass="more-option-popup popover-green custom-popover" container="body"
                        [placement]="'left'" [animation]="true" autoClose="true">
                        <i class="bi bi-three-dots-vertical copy-rate-sheet"></i>
                        <ng-template #popContent>
                            <div class="action-icons">
                                <button class="edit-btn" ngbTooltip="Download Rate Charge excel"
                                    (click)="rateSheetWeightChargeExcel(data)">
                                    <img src="/assets/images/svg/excel.svg" alt="excel" />
                                </button>

                                <button class="edit-btn" ngbTooltip="Copy Rate Sheet" (click)="copyRateSheet(data)">
                                    <i class="bi bi-copy copy-rate-sheet"></i>
                                </button>

                                <button class="edit-btn" [routerLink]="['/dashboard/rate-sheet/edit/' + data.id]"
                                    [state]="{ step:data?.rateSheetDetail?.step , firstLoad :true}"
                                    ngbTooltip="Edit Rate Sheet">
                                    <img src="/assets/images/icons/edit-icon.svg" alt="Edit" />
                                </button>

                                <button class="delete-btn" (click)="remove(data.id,resourceType)"
                                    ngbTooltip="Delete Rate Sheet">
                                    <img src="/assets/images/icons/delete-icon.svg" alt="delete" />
                                </button>
                            </div>
                        </ng-template>
                    </button>
                </div>
            </ng-template>
        </div>
    </div>
</div>
<ng-container *ngIf="isMapsReady; else loadingTpl">
    <google-map [center]="center" [zoom]="initialZoom" [height]="'95%'" [width]="'100%'" [options]="mapOptions">

        <!-- Circle -->
        <map-circle [center]="center" [radius]="radiusInMeters" [options]="circleOptions"></map-circle>

        <!-- Center Marker - FIXED: Use template reference -->
        <map-marker *ngIf="center.lat !== 0 && center.lng !== 0" #centerMarkerRef="mapMarker" [position]="center"
            [options]="{ icon: centerIcon, title: 'Center Location' }"
            (mapClick)="onCenterMarkerClick(centerMarkerRef)">
        </map-marker>

        <!-- Driver markers - FIXED: Use template reference -->
        <map-marker *ngFor="let driver of visibleDrivers; let i = index; trackBy: trackDriver"
            #driverMarkerRef="mapMarker" [position]="{ lat: driver.lat, lng: driver.lng }"
            [options]="{ icon: carIcon, title: driver.name }" (mapClick)="onDriverMarkerClick(driverMarkerRef, i)">
        </map-marker>

        <!-- Single info window -->
        <map-info-window>
            <div style="padding: 15px; min-width: 250px; max-width: 300px;">
                <!-- Center info content -->
                <div *ngIf="selectedInfoType === 'center'">
                    <h4 style="margin: 0 0 10px 0; color: #1976d2;">📍 Center Location</h4>
                    <div style="margin-bottom: 8px;">
                        <strong>Latitude:</strong> {{ center.lat | number:'1.6-6' }}
                    </div>
                    <div style="margin-bottom: 8px;">
                        <strong>Longitude:</strong> {{ center.lng | number:'1.6-6' }}
                    </div>
                    <div style="margin-bottom: 8px;">
                        <strong>Radius:</strong> {{ radiusInMeters }}m ({{ (radiusInMeters/1000) | number:'1.2-2' }}km)
                    </div>
                    <div style="margin-bottom: 8px;" *ngIf="visibleDrivers.length > 0">
                        <strong>Drivers in Range:</strong> {{ visibleDrivers.length }}
                    </div>
                    <div style="margin-bottom: 12px;" *ngIf="centerAddress">
                        <strong>Address:</strong><br>
                        <small style="color: #666;">{{ centerAddress }}</small>
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <button
                            style="padding: 8px 12px; border: 1px solid #1976d2; background: white; color: #1976d2; border-radius: 4px; cursor: pointer;"
                            (click)="copyCoordinates()">
                            📋 Copy Coords
                        </button>
                        <button
                            style="padding: 8px 12px; border: 1px solid #4caf50; background: white; color: #4caf50; border-radius: 4px; cursor: pointer;"
                            (click)="openInGoogleMaps()">
                            🗺️ Open Maps
                        </button>
                    </div>
                </div>

                <!-- Driver info content -->
                <div *ngIf="selectedInfoType === 'driver'">
                    <h4 style="margin: 0 0 10px 0; color: #ff6b35;">🚛 {{ selectedLocation?.name }}
                    </h4>
                    <div style="margin-bottom: 8px;">
                        <strong>Latitude:</strong> {{ selectedLocation.lat | number:'1.6-6' }}
                    </div>
                    <div style="margin-bottom: 8px;">
                        <strong>Longitude:</strong> {{ selectedLocation.lng | number:'1.6-6' }}
                    </div>
                    <div style="margin-bottom: 8px;" *ngIf="selectedLocation.distanceFromCenter">
                        <strong>Distance from Center:</strong> {{ selectedLocation.distanceFromCenter | number:'1.0-0'
                        }}m
                    </div>
                    <div style="margin-bottom: 12px;" *ngIf="selectedLocation.address">
                        <strong>Address:</strong><br>
                        <small style="color: #666;">{{ selectedLocation.address }}</small>
                    </div>

                    <div style="display: flex; gap: 8px;">
                        <button
                            style="padding: 8px 12px; border: 1px solid #1976d2; background: white; color: #1976d2; border-radius: 4px; cursor: pointer;"
                            (click)="copyDriverCoordinates()">
                            📋 Copy Coords
                        </button>
                        <button
                            style="padding: 8px 12px; border: 1px solid #4caf50; background: white; color: #4caf50; border-radius: 4px; cursor: pointer;"
                            (click)="openDriverInGoogleMaps()">
                            🗺️ Open Maps
                        </button>
                    </div>
                </div>
            </div>
        </map-info-window>

    </google-map>
</ng-container>

<ng-template #loadingTpl>
    <div class="p-4 text-center">Loading map…</div>
</ng-template>
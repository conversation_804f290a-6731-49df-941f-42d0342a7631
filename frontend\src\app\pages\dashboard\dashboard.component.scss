@import "../../../assets/scss/mixins.scss";
@import "../../../variables.scss";

.svg-white {
    filter: brightness(0) invert(1);
}

// Statistics Cards with AI-inspired design and animations
.stat-card {
    background: $white-color;
    border-radius: 16px;
    padding: 24px;
    height: 100%;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.08),
        0 0 0 1px rgba(255, 255, 255, 0.8),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--login-color), #2f3348);
        background-size: 200% 100%;
    }

    &::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        animation: shimmerEffect 4s ease-in-out infinite;
    }

    &:hover {
        transform: translateY(-12px) scale(1.02);
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.12),
            0 0 0 1px rgba(255, 255, 255, 0.9),
            inset 0 1px 0 rgba(255, 255, 255, 1);

        &::after {
            opacity: 1;
            animation: shimmer 1.5s ease;
        }

        .stat-card-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-card-value {
            transform: scale(1.05);
        }
    }

    .stat-card-content {
        @include flex-space-between-start;
        height: 100%;
        position: relative;
        z-index: 2;
    }

    .stat-card-info {
        flex: 1;

        .stat-card-title {
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-card-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
            line-height: 1;
            transition: transform 0.3s ease;
            animation: valuePulse 3s ease-in-out infinite;
        }

        .stat-card-change {
            @include d-flex-align-center;
            gap: 4px;
            font-size: $font-size-14;
            font-weight: 600;
            transition: all 0.3s ease;
            animation: changeGlow 4s ease-in-out infinite;

            &.positive {
                color: #10b981;
            }

            &.negative {
                color: #ef4444;
            }

            i {
                font-size: 0.75rem;
                transition: transform 0.3s ease;
                animation: iconBounce 2s ease-in-out infinite;
            }

            &:hover i {
                transform: scale(1.2);
            }
        }
    }

    .stat-card-icon {
        width: 46px;
        height: 46px;
        border-radius: 8px;
        @include flex-center;
        font-size: 1.3rem;
        background: var(--login-color);
        color: $white-color;
        flex-shrink: 0;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

        img {
            max-width: 100%;
            max-height: 100%;
            color: $white-color;
        }
    }
}


// Filter Section
.filter-section {
    .filter-title {
        color: #1e293b;
        font-weight: 700;
        margin-bottom: 16px;
        @include d-flex-align-center;

        i {
            margin-right: 12px;
            font-size: 1.3rem;
        }
    }
}

.duration-tab-filter {
    .tab-container {
        display: flex;
        padding: 4px;
        border: 1px solid var(--uploader-background-color);
        background: var(--button-pre-active-color);
        border-radius: 50px;
        width: fit-content;
    }

    .tab {
        padding: 8px 16px;
        border-radius: 50px;
        font-size: $font-size-16;
        cursor: pointer;
        color: $black-color;
        transition: all .3s;

        @media (min-width: 1200px) {
            padding: 8px 20px;

            span {
                font-size: $font-size-14;
            }
        }

        &.active {
            background: var(--primary-color);
            color: $white-color;
            min-width: 120px;
            @include flex-column-center;
        }

        &:hover:not(.active) {
            font-size: 16px;
        }
    }
}

// Charts Section
.charts-section {
    .modern-card {
        background: $white-color;
        border-radius: 16px;
        padding: 0;
        border: none;
        overflow: hidden;
        position: relative;
        transition: all .4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, .08), 0 0 0 1px rgba(0, 0, 0, .05);

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--login-color), #2f3348);
            background-size: 200% 100%;
        }

        &:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, .12), 0 0 0 1px rgba(0, 0, 0, .08);

            .chart-icon {
                transform: scale(1.1) rotate(5deg);
            }
        }

        .chart-header {
            @include flex-center;
            padding: 24px 24px 16px;
            border-bottom: 1px solid rgba(0, 0, 0, .1);
            background: #f8fafc;

            .chart-title-section {
                @include flex-center;
                gap: 16px;

                .chart-icon {
                    width: 48px;
                    height: 48px;
                    border-radius: 12px;
                    background: var(--login-color);
                    color: $white-color;
                    font-size: 1.2rem;
                    @include flex-center;
                    @include transition(all, .3s);
                }

                .chart-title-content {
                    .chart-title {
                        font-size: 1.25rem;
                        font-weight: 600;
                        color: #1e293b;
                        margin: 0 0 4px;
                        animation: titleGlow 5s ease-in-out infinite;
                    }

                    .chart-subtitle {
                        font-size: $font-size-14;
                        color: #64748b;
                        margin: 0;
                    }
                }
            }

            .chart-actions {
                .btn {
                    padding: 8px 16px;
                    border-radius: 12px;
                    font-size: $font-size-14;
                    background: $white-color;
                    color: #64748b;
                    border: 1px solid rgba(0, 0, 0, .1);
                    transition: all .3s;
                    animation: buttonGlow 6s ease-in-out infinite;

                    &:hover {
                        background: linear-gradient(135deg, var(--primary-color) 0%, var(--login-color) 100%);
                        border-color: var(--primary-color);
                        color: $white-color;
                        transform: translateY(-2px);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, .15);
                    }
                }
            }
        }

        .chart-container {
            padding: 24px;
            min-height: 350px;
            @include flex-center;
        }
    }
}

.no-data-box {
    text-align: center;
    padding: 2rem;
    color: $white-color;

    img {
        max-width: 160px;
        margin-bottom: 1rem;
        opacity: 0.75;
    }

    p {
        font-size: 16px;
        font-weight: 500;
    }
}

.no-data-wrapper {
    @include flex-center;
    min-height: 400px;
    background: $white-color;
    border-radius: 10px;
    box-shadow: 0 0 30px #0129701a;
    border-top: 4px solid var(--login-color)
}

// Dropdowns
.filter-dropdown {
    min-width: 240px;
    max-width: 100%;
    padding: 6px;
    border-radius: 12px;
    background: $white-color;
    color: #64748b;
    font-weight: $font-weight-500;
    border: 1px solid rgba(0, 0, 0, .1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, .05);
    transition: all .3s;

    @media (min-width: 375px) {
        max-width: 350px;
    }

    &:hover {
        background: linear-gradient(135deg, var(--primary-color) 0%, #667eea 100%);
        border-color: var(--primary-color);
        color: $white-color;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, .15);
    }
}

.dropdown-toggle {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: $white-color;
    font-size: $font-size-16;
    font-weight: $font-weight-600;
    padding: 0.5rem 1.2rem;
    @include flex-space-between;
    border-radius: 8px;

    &:hover,
    &:active {
        background: linear-gradient(135deg, var(--primary-color));
        color: $white-color;
        transform: translateY(-1px);
    }
}

.dropdown-menu {
    width: 100%;
    max-width: 100%;
    border-radius: 14px;
    font-size: 1rem;
    backdrop-filter: blur(10px);
    padding: 6px;
    top: -2px !important;
    border: 1px solid rgba(0, 0, 0, .1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, .15);
    animation: fadeIn .25s ease;

    &.show {
        border-top-left-radius: 0 !important;
        border-top-right-radius: 0 !important;
    }
}

.dropdown-toggle.show {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
}

.dropdown-item {
    font-weight: $font-weight-600;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: $font-size-14;
    border-radius: 8px;
    transition: all .3s;

    &:hover,
    &:focus,
    &:active {
        background: var(--primary-color);
        color: $white-color;
        padding: 8px;
    }
}

@media(min-width: 400px) {
    .dropdown-menu {
        max-width: 350px !important;
    }

}

// Responsive Layout
@media (max-width: 1200px) {
    .stat-card {
        padding: 20px;

        .stat-card-value {
            font-size: 2.2rem;
        }
    }
}

@media (max-width: 992px) {
    .stat-card {
        margin-bottom: 16px;
        padding: 20px;

        .stat-card-value {
            font-size: 2rem;
        }

        .stat-card-icon {
            align-self: center;
        }
    }

    .charts-section {
        .modern-card {
            margin-bottom: 16px;

            .chart-header {
                .chart-title-section {
                    width: 100%;
                }

                .chart-actions {
                    align-self: flex-end;
                }
            }

            .chart-container {
                padding: 16px;
                min-height: 300px;
            }
        }
    }
}

@media (max-width: 768px) {
    .stat-card {
        padding: 16px;
        margin-bottom: 12px;

        .stat-card-value {
            font-size: 1.75rem;
        }

        .stat-card-title {
            font-size: 0.8rem;
        }

        .stat-card-icon {
            align-self: center;
            width: 50px;
            height: 50px;
            font-size: 1.25rem;
        }
    }

    .filter-section {
        .filter-title {
            font-size: 1.2rem;
            margin-bottom: 12px;

            &::before {
                width: 40px;
                height: 2px;
            }
        }
    }

    .charts-section {
        .modern-card {
            margin-bottom: 12px;

            .chart-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
                padding: 16px 16px 12px;

                .chart-title-section {
                    width: 100%;
                    gap: 12px;

                    .chart-icon {
                        width: 40px;
                        height: 40px;
                        font-size: 1rem;
                    }

                    .chart-title-content {
                        .chart-title {
                            font-size: 1.1rem;
                        }

                        .chart-subtitle {
                            font-size: 0.8rem;
                        }
                    }
                }

                .chart-actions {
                    align-self: flex-end;

                    .btn {
                        padding: 6px 12px;
                        font-size: 0.8rem;
                    }
                }
            }

            .chart-container {
                padding: 8px;
                min-height: 250px;
            }
        }
    }
}

@media (max-width: 576px) {
    .stat-card {
        padding: 20px;
        margin-bottom: 8px;

        .stat-card-value {
            font-size: 1.5rem;
        }

        .stat-card-title {
            font-size: 0.75rem;
        }

        .stat-card-icon {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }
    }

    .filter-section {
        .filter-title {
            font-size: 1.1rem;

            &::before {
                width: 30px;
                height: 2px;
            }
        }
    }

    .charts-section {
        .modern-card {
            .chart-header {
                padding: 12px 12px 8px;

                .chart-title-section {
                    gap: 8px;

                    .chart-icon {
                        width: 36px;
                        height: 36px;
                        font-size: 0.9rem;
                    }

                    .chart-title-content {
                        .chart-title {
                            font-size: 1rem;
                        }

                        .chart-subtitle {
                            font-size: 0.75rem;
                        }
                    }
                }
            }

            .chart-container {
                padding: 8px;
                min-height: 200px;
            }
        }
    }
}



@media (max-width: 480px) {
    .stat-card {
        padding: 20px;

        .stat-card-value {
            font-size: 1.25rem;
        }

        .stat-card-title {
            font-size: 0.7rem;
        }

        .stat-card-icon {
            width: 36px;
            height: 36px;
            font-size: 0.9rem;
        }
    }

    .filter-section {
        .filter-title {
            font-size: 1rem;
        }
    }


}
import { Injectable } from '@angular/core';

import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { BaseService } from '../../../config/base.service';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';

@Injectable({
    providedIn: 'root',
})
export class NotificationsService extends BaseService {
    // 🔸 shared state
    private _count$ = new BehaviorSubject<number>(0);
    count$: Observable<number> = this._count$.asObservable();

    constructor(public override http: HttpClient) {
        super(http, '/api/notifications', '/api/notifications');
    }

    getNotificationCount(filterParam: FilterParam): Observable<RestResponse> {
        return this.getRecords('/api/Notifications/count', filterParam);
    }

    getNotifications(filterParam: FilterParam): Observable<RestResponse> {
        return this.getRecords('/api/Notifications/count', filterParam);
    }

    notificationAction(notifications: any, isDeleteAction?: boolean): Observable<RestResponse> {
        const url = `/api/${isDeleteAction ? 'delete' : 'view'}/notifications`;
        return this.getRecords(url, notifications);
    }

    notificationAllAction(isDeleteAction?: boolean): Observable<RestResponse> {
        const url = `/api/${isDeleteAction ? 'delete' : 'view'}/all/notifications`;
        return this.getRecords(url);
    }


    // API call only once when needed
    setCount(newCount: number): void {
        this._count$.next(newCount);
    }

    // decrease without API call
    decreaseCount(): void {
        const current = this._count$.getValue();
        this._count$.next(Math.max(0, current - 1));
    }
}

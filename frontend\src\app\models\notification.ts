import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/services/toast.service';
import { User } from './access/user';

export class Notification extends BaseModel {
    tenantId!: number;
    slug!: string;
    type!: string;
    title!: string;
    message!: string;
    user!: string;
    userDetail!: User;
    entityName!: string;
    entityPkId!: string;
    device!: string;
    isRead!: boolean;
    status?: string;
    iconClass!: string;

    constructor(data?: Partial<Notification>) {
        super();
        this.isDeleted = false;
        this.isActive = true;

        if (data) {
            Object.assign(this, data);
        }
    }

    static fromResponse(data: any): Notification {
        return new Notification(data);
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        if (this.isNullOrUndefinedAndEmpty(this.title)) {
            form.controls.name.setErrors({ invalid: true });
            return false;
        }
        return true;
    }

    forRequest() {
        this.title = this.trimMe(this.title);
        this.message = this.trimMe(this.message);

        return this;
    }
}

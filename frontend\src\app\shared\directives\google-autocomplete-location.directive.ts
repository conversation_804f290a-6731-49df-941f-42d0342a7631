import { Directive, ElementRef, EventEmitter, Input, Ng<PERSON><PERSON>, On<PERSON>estroy, OnInit, Output } from '@angular/core';
declare const google: any;

@Directive({
    selector: '[appGoogleAutocomplete]',
    standalone: true,
})
export class GoogleAutocompleteDirective implements OnInit, OnDestroy {
    @Output() placeChanged = new EventEmitter<any>();
    private autocomplete: any;

    @Input() types: string[] = ['geocode'];

    // Optional: allow passing country restriction
    @Input() countryRestriction: string[] = ['ca'];

    constructor(
        private el: ElementRef,
        private ngZone: NgZone,
    ) {}

    ngOnInit(): void {
        // Ensure Google Maps is loaded before initializing
        this.waitForGoogleMaps().then(() => {
            this.initializeGoogleAutocomplete();
        });
    }

    private waitForGoogleMaps(): Promise<void> {
        return new Promise((resolve) => {
            const checkGoogleMaps = () => {
                if ((window as any).google?.maps?.places) {
                    resolve();
                } else {
                    setTimeout(checkGoogleMaps, 100);
                }
            };
            checkGoogleMaps();
        });
    }

    private initializeGoogleAutocomplete(): void {
        const options = {
            types: this.types,
            componentRestrictions: { country: this.countryRestriction },
        };

        this.autocomplete = new google.maps.places.Autocomplete(this.el.nativeElement, options);

        this.autocomplete.addListener('place_changed', () => {
            this.ngZone.run(() => {
                const place = this.autocomplete.getPlace();
                const formatted = place?.formatted_address ?? place?.name;

                if (formatted) {
                    // Manually update the input value and trigger Angular's change detection
                    this.el.nativeElement.value = formatted;

                    // Dispatch an input event to notify Angular
                    const inputEvent = new Event('input', { bubbles: true });
                    this.el.nativeElement.dispatchEvent(inputEvent);

                    // Emit the place to the parent component
                    this.placeChanged.emit(place);
                }
            });
        });
    }

    ngOnDestroy(): void {
        if (this.autocomplete && google?.maps?.event) {
            google.maps.event.clearInstanceListeners(this.autocomplete);
        }
    }
}

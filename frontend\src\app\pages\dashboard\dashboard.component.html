<div class="main-site-container">
    <div class="body-content-container">
        <!-- Duration Filter Section -->
        <div class="filter-section p-md-2">
            <!-- Mobile Dropdown -->
            <div class="dropdown d-flex d-md-none align-items-center justify-content-end mb-3">
                <div ngbDropdown #myDrop="ngbDropdown" class="w-100 ngbs-dropdown d-flex justify-content-end">
                    <button type="button" class="btn btn-outline-primary filter-dropdown w-100" id="dropdownManual"
                        ngbDropdownToggle>
                        {{ "DashboardFilter.durationFilter" | translate }}
                    </button>
                    <div ngbDropdownMenu aria-labelledby="dropdownManual">
                        <button *ngFor="let option of durationOptions" ngbDropdownItem
                            (click)="setActiveTab(option.value)">
                            {{ option.label | translate }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Desktop Tabs -->
            <div class="d-flex flex-column flex-xl-row justify-content-end mb-2 mb-md-4 me-2 duration-tab-filter">
                <div class="d-none d-md-flex align-items-center ms-3 ms-xl-0 me-xl-3 mb-2 mb-xl-0">
                    <h6 class="filter-title mb-0">
                        <span>
                            {{ "DashboardFilter.durationFilter" | translate }}
                        </span>
                    </h6>
                </div>

                <div class="tab-container d-none d-md-flex">
                    <div class="tab" *ngFor="let option of durationOptions" (click)="setActiveTab(option.value)"
                        [class.active]="durationPeriod === option.value">
                        <span class="tab-icon fw-600">
                            <i class="bi bi-calendar3"></i>
                            {{ option.label | translate }}
                        </span>
                    </div>
                </div>
            </div>

        </div>
        <!-- Statistics Cards Row -->
        <div class="row mb-4">
            <div class="col-12 col-sm-6 col-xl-3 mb-2 mb-xl-0">
                <div class="stat-card  cursor-pointer">
                    <div class="stat-card-content" (click)="onEmployeeSelect($event)">
                        <div class="stat-card-info">
                            <h3 class="stat-card-title">{{ "USERS.objNames" | translate }}</h3>
                            <div class="stat-card-value">{{ dashboardStats?.totalEmployees
                                }}</div>
                        </div>
                        <div class="stat-card-icon">
                            <i class="bi bi-people"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-6 col-xl-3 mb-2 mb-xl-0">
                <div class="stat-card cursor-pointer">
                    <div class="stat-card-content" (click)="onCustomerSelect($event)">
                        <div class="stat-card-info">
                            <h3 class="stat-card-title">{{ "Quotation.customer" | translate }}</h3>
                            <div class="stat-card-value">{{
                                dashboardStats?.totalCustomers }}</div>
                        </div>
                        <div class="stat-card-icon">
                            <img class="svg-white" src="/assets/images/svg/menu/customers.svg" alt="Customer Icon">
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-6 col-xl-3 mb-2 mb-xl-0">
                <div class="stat-card  cursor-pointer">
                    <div class="stat-card-content" (click)="onRateCardSelect($event)">
                        <div class="stat-card-info">
                            <h3 class="stat-card-title">{{ "RateSheetWeightCharge.rateSheet" | translate }}</h3>
                            <div class="stat-card-value">{{
                                dashboardStats?.totalRateSheets }}</div>
                        </div>
                        <div class="stat-card-icon">
                            <img class="svg-white" height="25" src="/assets/images/svg/menu/price-list.svg"
                                alt="Rate Sheet Icon">
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!-- Charts Section -->
        <div class="charts-section">
            <div class="row">
                <!-- Shipment Pie Chart -->
                <div class="col-12 col-xl-6 mb-3" *ngIf="shipmentStatus?.length">
                    <div class="modern-card">
                        <div class="chart-header">
                            <div class="chart-title-section">
                                <div class="chart-icon">
                                    <img class="svg-white" height="25" src="/assets/images/svg/menu/delivery-truck.svg"
                                        alt="Rate Sheet Icon">
                                </div>
                                <div class="chart-title-content">
                                    <h3 class="chart-title">{{"SHIPMENT.objNames" | translate}}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="chart-container">
                            @defer {
                            <ngx-charts-pie-chart [results]="shipmentStatus" [view]="view" [legend]="false"
                                [labels]="true" [explodeSlices]="false" [animations]="true" [arcWidth]="0.4"
                                [customColors]="customShipmentColors" (select)="onShipmentSelect($event)">
                            </ngx-charts-pie-chart>
                            }
                        </div>
                    </div>
                </div>

                <!-- Quotation Bar Graph -->
                <div class="col-12 col-xl-6 mb-3" *ngIf="quotationStatus?.length">
                    <div class="modern-card">
                        <div class="chart-header">
                            <div class="chart-title-section">
                                <div class="chart-icon">
                                    <img class="svg-white" height="25" src="/assets/images/svg/menu/app.svg"
                                        alt="Rate Sheet Icon">
                                </div>
                                <div class="chart-title-content">
                                    <h3 class="chart-title">{{"Quotation.objNames" | translate}}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="chart-container">
                            @defer {
                            <ngx-charts-bar-vertical [results]="quotationStatus" [legend]="false" [xAxis]="true"
                                [view]="view" [customColors]="customQuotationColors" [yAxis]="true"
                                [showXAxisLabel]="true" [showYAxisLabel]="true" [animations]="true"
                                (select)="onQuotationSelect($event)">
                            </ngx-charts-bar-vertical>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- No Data Section -->
    <div *ngIf="showBlankBox" class="no-data-wrapper mx-md-2">
        <div class="no-data-box">
            <div class="no-data-icon">
                <img src="../../../../assets/images/svg/no-data.svg" alt="No Data" class="no-notifications-img" />
            </div>
            <h3 class="no-data-title">{{ "DASHBOARD.noDataTitle" | translate }}</h3>
            <p class="no-data-description">{{ "DASHBOARD.noDataDescription" | translate }}</p>
        </div>
    </div>
</div>
import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/services/toast.service';
import { Employees } from './customer/employees';
export class Driver extends BaseModel {
    tenantId!: number;
    slug!: string;
    driverCode!: string;
    employeeDetail!: Employees;
    user!: string;
    doj!: Date;
    passportNumber!: string;
    emergencyContactNumber!: string;
    emergencyContactName!: string;
    name!: string;

    constructor() {
        super();
        this.isDeleted = false;
        this.isActive = true;
    }

    static fromResponse(data: any): Driver {
        const driver = { ...data };

        return driver;
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        this.driverCode = this.trimMe(this.driverCode);
        this.passportNumber = this.trimMe(this.passportNumber);
        this.emergencyContactNumber = this.trimMe(this.emergencyContactNumber);
        this.emergencyContactName = this.trimMe(this.emergencyContactName);
        this.name = this.trimMe(this.name);
        return this;
    }
}

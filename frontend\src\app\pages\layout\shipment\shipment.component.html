<div class="site-main-container">
    <div class="position-relative">
        <div class="dashboard-main-filter-section px-0">
            <div class="d-flex gap-2 gap-sm-4 flex-wrap">
                <div class="custom-input-group custom-search-bar-outer mb-sm-0">
                    <input class="form-control search-form-control custom-search-bar" placeholder="Search by REFID..."
                        appDelayedInput (delayedInput)="search($event)" [delayTime]="1000" #searchInput />
                    <i class="bi bi-search"></i>
                </div>
                <div class="d-flex align-items-center gap-2 flex-wrap">
                    <label class="form-label m-0" for="show">Show</label>
                    <div class="dynamic-page-lenght-ng-select">
                        <ng-select [items]="pageLengths" bindLabel="label" bindValue="value"
                            [(ngModel)]="selectedPageLength" (change)="onPageLengthChange($event)" placeholder="Select"
                            [searchable]="false" [clearable]="false">
                        </ng-select>
                    </div>
                    <label class="form-label m-0" for="enteries">Entries</label>
                </div>
            </div>

            <!-- Buttons -->
            <div class="d-flex flex-sm-row gap-2" *ngIf="!detailPage">
                <!-- Approve Selected -->
                @if (checkedPendingAndCompletedStatus) {
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    [class.d-none]="records.length < 1" (click)="invoiceSelectedClick()">
                    <span class="text-center">
                        <img src="/assets/images/icons/invoice.svg" alt="invoice-icon" loading="eager" />
                    </span>
                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{
                        'SHIPMENT.invoice' | translate
                        }}</span>
                </button>
                }
                <!-- Filter Button -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    (click)="accordion.toggle('filter')">
                    <span>
                        <img src="/assets/images/icons/Filter_icon.svg" alt="filter-icon" loading="eager" />
                    </span>

                    <span class="d-sm-none d-md-inline fw-medium ms-1 me-3 custom-text-button">{{ 'COMMON.FILTER' |
                        translate }}
                    </span>
                </button>

                <!-- Export to Excel Button  -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    [class.d-none]="records.length < 1" (click)="exportToExcel(filterParam)">
                    <span class="text-center">
                        <img src="/assets/images/icons/export.svg" alt="export-icon" loading="eager" />
                    </span>

                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{
                        'UserPermission.export' | translate
                        }}</span>
                </button>

                <!-- Add New Button  -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    [routerLink]="['/dashboard/shipment/edit/0']" [state]="{ step: 1, firstLoad: true }">
                    <span class="text-center">
                        <img src="/assets/images/icons/Add_icon.svg" alt="Add-icon" loading="eager" />
                    </span>

                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{ 'COMMON.ADDNEW' | translate }}</span>
                </button>
            </div>
        </div>

        <!-- Accordion -->
        <div ngbAccordion #accordion="ngbAccordion" class="mt-2">
            <div ngbAccordionItem="filter" class="border-0">
                <div ngbAccordionCollapse>
                    <div ngbAccordionBody class="filter-container p-4 w-100">
                        <ng-template>
                            <div class="row gx-0 gy-3">
                                <!-- Pickup Address  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="city" bindValue="city" name="StatusFilter"
                                            #StatusFilter="ngModel" [(ngModel)]="filterParam.filtering.pickUpCity"
                                            [items]="pickupCities" [typeahead]="searchPickupCitySubject"
                                            [loading]="loadingPickupCityNgSelect">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            'RATE_SHEET.pickupCity' | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!-- Delivery Address  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="city" bindValue="city" name="StatusFilter"
                                            #StatusFilter="ngModel" [(ngModel)]="filterParam.filtering.deliveryCity"
                                            [typeahead]="searchDeliveryCitySubject" [items]="deliveryCities"
                                            [loading]="loadingDeliveryCityNgSelect">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            'RATE_SHEET.deliveryCity' | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!-- Customer  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-xl-2 ps-xl-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="fullName" bindValue="id" name="StatusFilter"
                                            #StatusFilter="ngModel" [(ngModel)]="filterParam.filtering.customerId"
                                            [items]="customers" [typeahead]="searchCustomerSubject"
                                            [loading]="loadingCustomerNgSelect">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            'CUSTOMER.objName' | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!-- Driver  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 pe-xl-0 ps-md-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="fullName" bindValue="id" name="StatusFilter"
                                            #StatusFilter="ngModel" [(ngModel)]="filterParam.filtering.driverId"
                                            [items]="drivers" [typeahead]="searchDriverSubject"
                                            [loading]="loadingDriverNgSelect">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            'BARCODE.driver' | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!-- Shipment Type  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="name" bindValue="id" name="StatusFilter"
                                            #StatusFilter="ngModel" [(ngModel)]="filterParam.filtering.shipmentType"
                                            [items]="shipmentTypeOptions">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            'SHIPMENT.shipmentType' | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!-- Status  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-xl-2 ps-xl-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="name" bindValue="id" name="StatusFilter"
                                            #StatusFilter="ngModel" [(ngModel)]="filterParam.filtering.status"
                                            [items]="shipmentStatusOptions">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            'SHIPMENT.shipmentStatus' | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!-- Payment Type  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="name" bindValue="id" name="StatusFilter"
                                            #StatusFilter="ngModel" [(ngModel)]="filterParam.filtering.paymentType"
                                            [items]="shipmentPaymentTypeOptions">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            'SHIPMENT.paymentType' | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!-- PaymentStatus  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 pe-xl-0 ps-md-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="name" bindValue="id" name="StatusFilter"
                                            #StatusFilter="ngModel" [(ngModel)]="filterParam.filtering.paymentStatus"
                                            [items]="shipmentPaymentStatusOptions">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            'SHIPMENT.paymentStatus' | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!-- Created on  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-xl-2 ps-xl-0 mt-12">
                                    <app-ng-custom-date-range-picker ngDefaultControl name="selectDateRange"
                                        [labelName]="'RATE_SHEET.startDateEndDate' | translate"
                                        [inputFromDate]="filterParam.filtering.fromDate"
                                        [inputToDate]="filterParam.filtering.toDate"
                                        (setFromDate)="filterParam.filtering.fromDate = $event"
                                        (setToDate)="filterParam.filtering.toDate = $event"
                                        [customZIndex]="'z-index-3'"></app-ng-custom-date-range-picker>
                                </div>
                                <!-- Duration Filter -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="name" bindValue="id" name="durationFilter"
                                            #DurationFilter="ngModel"
                                            [(ngModel)]="filterParam.filtering.createdWithinDays"
                                            [items]="durationFilter">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            'DashboardFilter.durationFilter' | translate
                                            }}</label>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-end gap-2 mt-12">
                                    <button class="btn btn-primary custom-small-button" (click)="onApplyFilter()"
                                        appRippleEffect>
                                        {{ 'COMMON.APPLY' | translate }}
                                    </button>
                                    <button class="btn btn-primary custom-small-button"
                                        (click)="onClearFilter(searchInput)" appRippleEffect>
                                        {{ 'COMMON.CLEAR' | translate }}
                                    </button>
                                </div>
                            </div>
                        </ng-template>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Table Container -->
    <div class="col-md-12">
        <!-- Table Layout -->
        <div class="site-table-container">
            <div class="table-responsive" [ngClass]="{ 'has-records': shipments.length > 0 }">
                <table class="table table-hover site-table-layout" datatable [dtOptions]="dtOptions"
                    [dtTrigger]="dtTrigger">
                    <thead class="table-head">
                        <tr>
                            <th>{{ 'BARCODE.refID' | translate }}</th>
                            <th>{{ 'CUSTOMER.objName' | translate }}</th>
                            <th>{{ 'BARCODE.driver' | translate }} {{ 'USERS.Name' | translate }}</th>
                            <th>{{ 'SHIPMENT.shipmentType' | translate }}</th>
                            <th>{{ 'RATE_SHEET.objName' | translate }}</th>
                            <th>{{ 'RATE_SHEET.pickupCity' | translate }}</th>
                            <th>{{ 'RATE_SHEET.deliveryCity' | translate }}</th>
                            <th>{{ 'BARCODE.objName' | translate }}</th>
                            <th>{{ 'SHIPMENT.shipmentStatus' | translate }}</th>
                            <th>{{ 'SHIPMENT.paymentType' | translate }}</th>
                            <th>{{ 'SHIPMENT.paymentStatus' | translate }}</th>
                            <th class="text-end">{{ 'SHIPMENT.total' | translate }}</th>
                            <th>{{ 'SHIPMENT.ETD' | translate }}</th>
                            <th>{{ 'COMMON.CREATED_ON' | translate }}</th>
                            <th class="th-action pe-2">
                                <div class="d-flex gap-2">
                                    <span class="ms-2">
                                        <input [checked]="isAllShipmentInvoicedSelected"
                                            class="form-check-input cursor-pointer" id="allShipmentInvoicedSelected"
                                            name="allUnapprovedSelect" type="checkbox" />
                                    </span>
                                    <span>{{ 'COMMON.ACTION' | translate }}</span>
                                </div>
                            </th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>

        <!-- Table Body -->
        <div class="table-container-body">
            <ng-template #refID let-data="adtData">
                <a (click)="onEdit(data)"><strong>{{ data.refID }}</strong></a>
            </ng-template>

            <ng-template #customer let-data="adtData">
                <span class="action-button text-capitalize ellipsis-2-line"
                    [appTooltipEllipsis]="data?.customerUserDetail?.fullName" ngbTooltip>
                    {{ data?.customerUserDetail?.fullName }}
                </span>
            </ng-template>

            <ng-template #driver let-data="adtData">
                <span class="action-button text-capitalize ellipsis-2-line"
                    [appTooltipEllipsis]="data?.driverUserDetail?.fullName" ngbTooltip>
                    {{ data?.driverUserDetail?.fullName }}
                </span>
            </ng-template>

            <ng-template #shipmentType let-data="adtData">
                <div class="d-flex" *ngIf="data.shipmentType">
                    <span [appStatusBadge]="data?.shipmentType | removeUnderscore"></span>
                </div>
            </ng-template>

            <ng-template #rateSheet let-data="adtData">
                <span class="ellipsis-2-line" [appTooltipEllipsis]="data?.rateSheetDetail?.name" ngbTooltip>{{
                    data?.rateSheetDetail?.name }}
                </span>
            </ng-template>

            <ng-template #pickupAddress let-data="adtData">
                <span class="ellipsis-2-line" [appTooltipEllipsis]="data?.pickupAddressDetail?.city" ngbTooltip>{{
                    data?.pickupAddressDetail?.city
                    }}</span>
            </ng-template>

            <ng-template #deliveryAddress let-data="adtData">
                <span class="ellipsis-2-line" [appTooltipEllipsis]="data.deliveryAddressDetail?.city" ngbTooltip>{{
                    data?.deliveryAddressDetail?.city }}
                </span>
            </ng-template>

            <ng-template #barCode let-data="adtData">
                <span>{{ data?.barCodeDetail?.barCodeNo }} </span>
            </ng-template>

            <ng-template #shipmentStatus let-data="adtData">
                <div class="d-flex" *ngIf="data.status">
                    <span (click)="
                            data.status === 'DELIVERED' && userRole === 'ROLE_SUPER_ADMIN' && updateShipmentStatus(data)
                        " [editable]="data.status === 'DELIVERED' && userRole === 'ROLE_SUPER_ADMIN'"
                        [appStatusBadge]="data?.status | removeUnderscore"></span>
                </div>
            </ng-template>

            <ng-template #paymentType let-data="adtData">
                <div class="d-flex" *ngIf="data.paymentType">
                    <span [appStatusBadge]="data?.paymentType | removeUnderscore"></span>
                </div>
            </ng-template>

            <ng-template #paymentStatus let-data="adtData">
                <div class="d-flex" *ngIf="data.paymentStatus">
                    <span [appStatusBadge]="data?.paymentStatus | removeUnderscore"></span>
                </div>
            </ng-template>

            <ng-template #grandTotal let-data="adtData">
                <div class="td-align-right">{{ data.grandTotal | currency }}</div>
            </ng-template>

            <ng-template #etd let-data="adtData">
                <span>{{ data.etd | dateFormat }} </span>
            </ng-template>

            <ng-template #createdOn let-data="adtData">
                <span>{{ data.createdOn | dateFormat }} </span>
            </ng-template>

            <ng-template #action let-data="adtData">
                <div class="action-icons d-flex justify-content-center">
                    <button class="delete-btn" [ngClass]="{
                            'cursor-not-allowed no-hover': !(
                                data?.paymentStatus === 'PENDING' && data?.status === 'COMPLETED'
                            ),
                        }">
                        <input class="form-check-input cursor-pointer m-0" id="isChecked{{ data.id }}"
                            name="isChecked{{ data.id }}" type="checkbox"
                            [disabled]="!(data?.paymentStatus === 'PENDING' && data?.status === 'COMPLETED')"
                            (change)="selectCheckbox(data, $event)" [checked]="
                                isAllShipmentInvoicedSelected &&
                                data?.paymentStatus === 'PENDING' &&
                                data?.status === 'COMPLETED'
                            " />
                    </button>
                    <button class="edit-btn" [ngbPopover]="popContent"
                        popoverClass="more-option-popup popover-green custom-popover" container="body"
                        [placement]="'left'" [animation]="true" autoClose="true">
                        <i class="bi bi-three-dots-vertical copy-rate-sheet"></i>
                        <ng-template #popContent>
                            <div class="action-icons">
                                <button *ngIf="!customerId" class="delete-btn" (click)="exportToPDF(data.id)"
                                    ngbTooltip="Download PDF">
                                    <img src="/assets/images/icons/Pdf-icon.svg" alt="delete" />
                                </button>

                                <button *ngIf="!customerId" class="delete-btn" (click)="remove(data.id, resourceType)"
                                    ngbTooltip="Delete Shipment">
                                    <img src="/assets/images/icons/delete-icon.svg" alt="delete" />
                                </button>

                                <button class="view maps" (click)="openModal(content, data)"
                                    ngbTooltip="View Shipment Maps">
                                    <i class="bi bi-geo-alt-fill font-size-18" alt="view maps"></i>
                                </button>
                            </div>
                        </ng-template>
                    </button>
                    <button class="edit-btn" (click)="onEdit(data)" ngbTooltip="Edit Shipment">
                        <img src="/assets/images/icons/edit-icon.svg" alt="Edit" />
                    </button>
                </div>
            </ng-template>
        </div>
    </div>
</div>

<ng-template #content let-modal>
    <div class="modal-header mx-2 mx-md-4">
        <h4 class="modal-title">View Maps</h4>
        <button type="button" class="btn-close modal-close" aria-label="Close"
            (click)="modal.dismiss('Cross click')"></button>
    </div>

    <div class="modal-body pt-0 mx-2 mx-md-4">
        <div class="map-wrapper">
            <app-map [apiKey]="googleKey" [drivers]="driverLists" [centerPoint]="centerPoint"
                [shipment]="true"></app-map>
        </div>
    </div>
</ng-template>
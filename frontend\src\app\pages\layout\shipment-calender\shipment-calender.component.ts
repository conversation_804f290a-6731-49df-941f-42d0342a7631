// Angular core imports (highest priority)
import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, NgZone, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

// FullCalendar imports
import { FullCalendarComponent, FullCalendarModule } from '@fullcalendar/angular';
import { CalendarOptions, MoreLinkArg } from '@fullcalendar/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import listPlugin from '@fullcalendar/list';

// third party imports
import { NgbAccordionModule, NgbModal, NgbModalRef, NgbPopoverModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { map, Subject, Subscription } from 'rxjs';

// Application models
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';
import { Customer } from '../../../models/customer/customer';

// Application core services
import { LoadingService } from '../../../services/loading.service';
import { CalendarResizeService } from '../../../shared/services/resize-notifier.service';
import { ToastService } from '../../../shared/services/toast.service';

// Manager
import { CustomerManager } from '../customer/customer.manager';
import { ShipmentManager } from '../shipment/shipment.manager';

// Application managers
import { Constant } from '../../../config/constants';
import { User } from '../../../models/access/user';
import { StatusBadgeDirective } from '../../../shared/directives/status-color-badge.directive';
import { RemoveUnderscorePipe } from '../../../shared/pipes/remove-underscore.pipe';
import { TypeAheadFilterService } from '../../../shared/services/typeahead.service';
import { EmployeesManager } from '../employees/employees.manager';

interface ShipmentEvent {
    id: string;
    title: string;
    date: string;
    step: number;
    customer: string;
    status: string;
    paymentStatus: string;
    driver: string;
    deliveryCity: string;
    pickupCity: string;
    totalAmount: number;
}

@Component({
    selector: 'app-shipment-calender',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        FullCalendarModule,
        NgbPopoverModule,
        RemoveUnderscorePipe,
        StatusBadgeDirective,
        TranslateModule,
        NgbAccordionModule,
        NgSelectModule,
    ],
    templateUrl: './shipment-calender.component.html',
    styleUrl: './shipment-calender.component.scss',
})
export class ShipmentCalenderComponent implements OnInit, OnDestroy {
    @ViewChild('calendar') calendarComponent!: FullCalendarComponent;
    @ViewChild('content', { static: true }) contentTemplate!: TemplateRef<any>;

    activeModalRef?: NgbModalRef;
    resizeSub!: Subscription;

    events!: ShipmentEvent;

    latestMoreLinkArg: any;
    latestMouseEvent: MouseEvent | null = null;

    isLoadingEvents: boolean = false;

    modalRef!: NgbModalRef;
    title!: string;
    filterParam!: FilterParam;
    month!: string;

    pickupCities: any[] = [];
    deliveryCities: any[] = [];
    customers: Customer[] = [];
    drivers: User[] = [];

    searchPickupCitySubject: Subject<string> = new Subject<string>();
    searchDeliveryCitySubject: Subject<string> = new Subject<string>();
    searchCustomerSubject: Subject<string> = new Subject<string>();
    searchDriverSubject: Subject<string> = new Subject<string>();

    loadingDriverNgSelect!: boolean;
    loadingPickupCityNgSelect!: boolean;
    loadingDeliveryCityNgSelect!: boolean;
    loadingCustomerNgSelect!: boolean;

    shipmentStatusOptions = Constant.SHIPMENT_STATUS_OPTIONS;
    shipmentPaymentStatusOptions = Constant.SHIPMENT_PAYMENT_STATUS_OPTIONS;

    calendarOptions: CalendarOptions = {
        plugins: [dayGridPlugin, listPlugin],
        initialView: window.innerWidth < 768 ? 'listWeek' : 'dayGridMonth',
        headerToolbar: {
            left: 'prev,next',
            center: 'title',
            right: '',
        },
        height: 'auto',
        dayMaxEvents: 3,
        nowIndicator: true,
        moreLinkClick: ((arg: MoreLinkArg) => {
            this.latestMoreLinkArg = arg;
            this.latestMouseEvent = arg.jsEvent as MouseEvent;

            this.manageDynamicPopoverPosition(
                '.fc-popover',
                this.latestMouseEvent,
                '.calendar-scroll-container',
                50,
                100,
            );

            return 'popover';
        }) as any,
        datesSet: (arg) => this.handleDateChange(arg),
        eventClick: (arg) => {
            const isFromPopover = (arg.jsEvent.target as HTMLElement)?.closest('.fc-popover');
            this.loadingService.show();

            if (isFromPopover) {
                setTimeout(() => this.handleEventClick(arg.event), 50);
            } else {
                this.handleEventClick(arg.event);
            }
        },
        validRange: {
            end: this.getTodayEndDate(),
        },
        events: [],
    };

    searchConfigs = [
        {
            subject: this.searchPickupCitySubject,
            fetchFunction: (param: FilterParam) => this.shipmentManager.fetchForPickupCityDropdown(param),
            updateResults: (results: any[]) => (this.pickupCities = results),
            updateLoading: (isLoading: boolean) => (this.loadingPickupCityNgSelect = isLoading),
            selectedItemGetter: () =>
                this.pickupCities.find(city => city.id === this.filterParam.filtering.pickUpCity),
        },
        {
            subject: this.searchDeliveryCitySubject,
            fetchFunction: (param: FilterParam) => this.shipmentManager.fetchForPickupCityDropdown(param),
            updateResults: (results: any[]) => (this.deliveryCities = results),
            updateLoading: (isLoading: boolean) => (this.loadingDeliveryCityNgSelect = isLoading),
            selectedItemGetter: () =>
                this.deliveryCities.find(city => city.id === this.filterParam.filtering.deliveryCity),
        },
        {
            subject: this.searchCustomerSubject,
            fetchFunction: (param: FilterParam, endpoint?: string) => this.customerManager.fetchForDropdownData(param, endpoint),
            updateResults: (results: any[]) => (this.customers = results),
            updateLoading: (isLoading: boolean) => (this.loadingCustomerNgSelect = isLoading),
            selectedItemGetter: () => this.customers.find(customer => customer.id === this.filterParam.filtering.customerId),
            endpoint: 'shipment/selection',
        },
        {
            subject: this.searchDriverSubject,
            fetchFunction: (param: FilterParam) => this.employeesManager.fetchForDriverDropdown(param),
            updateResults: (results: any[]) => (this.drivers = results),
            updateLoading: (isLoading: boolean) => (this.loadingDriverNgSelect = isLoading),
            selectedItemGetter: () =>
                this.drivers.find(driver => driver.id === this.filterParam.filtering.driverId),
        }
    ];

    constructor(
        public modalService: NgbModal,
        public calendarResizeService: CalendarResizeService,
        protected customerManager: CustomerManager,
        protected typeAheadService: TypeAheadFilterService,
        protected shipmentManager: ShipmentManager,
        protected employeesManager: EmployeesManager,
        private loadingService: LoadingService,
        private toastService: ToastService,
        private ngZone: NgZone,
        private cdr: ChangeDetectorRef,
    ) {
        this.setupAllSearches();
    }

    ngOnInit() {
        this.filterParam = new FilterParam();

        this.resizeSub = this.calendarResizeService.resize$.subscribe(() => {
            this.calendarComponent?.getApi()?.updateSize();
        });
    }

    ngOnDestroy() {
        this.resizeSub?.unsubscribe();
    }

    closeModal() {
        if (this.activeModalRef) {
            this.activeModalRef.close();
            this.activeModalRef = undefined;
        }
    }

    handleDateChange(arg: any) {
        if (this.isLoadingEvents) return; // prevent multiple calls

        const startDate = arg.view.currentStart;
        const year = startDate.getFullYear();
        const month = String(startDate.getMonth() + 1).padStart(2, '0');
        const formattedMonth = `${year}-${month}`;

        this.month = formattedMonth;
        const filters = new FilterParam();
        filters.filtering.month = this.month;

        this.loadCalendarEvents(this.filterParam);
    }

    handleEventClick(event: any): void {
        // Clean up popover
        const popoverEl = document.querySelector('.fc-popover');
        if (popoverEl) {
            popoverEl.remove(); // or use popoverEl.parentElement?.removeChild(popoverEl);
        }

        // Blur any focused element
        if (document.activeElement instanceof HTMLElement) {
            document.activeElement.blur();
        }
        this.cdr.detectChanges();

        // ⏳ (Optional) Short delay to allow popover DOM removal animation to finish
        setTimeout(() => {
            // 🔁 Assign values
            this.events = {
                id: event.id,
                title: event.title,
                date: event.startStr,
                step: event.extendedProps?.step ?? 1,
                customer: event.extendedProps?.customer ?? '',
                status: event.extendedProps?.status ?? '',
                paymentStatus: event.extendedProps?.paymentStatus ?? '',
                driver: event.extendedProps?.driver ?? '',
                deliveryCity: event.extendedProps?.deliveryCity ?? '',
                pickupCity: event.extendedProps?.pickupCity ?? '',
                totalAmount: event.extendedProps?.totalAmount ?? 0,
            };

            // 🚀 Open modal after DOM is clean
            this.openModal();
        }, 50); // 50ms is usually enough
    }

    loadCalendarEvents(filterParam: FilterParam) {
        if (this.isLoadingEvents) return;

        this.isLoadingEvents = true;

        filterParam.filtering.month = this.month;

        this.loadingService.show();

        this.shipmentManager
            .fetchShipmentCalenderDetails(filterParam)
            .pipe(
                map((response: RestResponse) => {
                    const data = response?.data ?? [];
                    return data.map((item: any) => ({
                        title: item.refID,
                        date: item.createdOn.split('T')[0],
                        color: this.getColorForStatus(item?.status),
                        id: item.id,
                        step: item.step ?? 1,
                        customer: item.customerUserDetail?.fullName ?? '',
                        status: item.status ?? '',
                        paymentStatus: item.paymentStatus ?? '',
                        driver: item.driverUserDetail?.fullName ?? '',
                        totalAmount: item.totalAmount ?? 0,
                        pickupCity: item.pickupAddressDetail?.city ?? '',
                        deliveryCity: item.deliveryAddressDetail?.city ?? '',
                    }));
                }),
            )
            .subscribe({
                next: (events) => {
                    this.updateCalendarEvents(events);
                    this.loadingService.hide();
                    this.isLoadingEvents = false;
                },
                error: (error) => {
                    this.toastService.error(error?.message);
                    this.loadingService.hide();
                    this.isLoadingEvents = false;
                },
            });
    }

    openModal() {
        if (!this.contentTemplate) {
            console.warn('Modal content template not found', this.contentTemplate);
            return;
        }

        this.ngZone.run(() => {
            setTimeout(() => {
                this.activeModalRef = this.modalService.open(this.contentTemplate, {
                    backdrop: 'static',
                    centered: true,
                    size: 'md',
                });

                this.loadingService.hide();
            }, 0); // 0ms still works to let DOM update
        });

        this.loadingService.hide();
    }

    onClearFilter() {
        const filters = this.filterParam.filtering;

        const hasFilters = Object.values(filters).some((value) => value !== undefined);
        if (!hasFilters) {
            return;
        }

        Object.keys(filters).forEach((key) => {
            (filters as Record<string, any>)[key] = undefined;
        });

        this.filterParam.filtering.month = this.month; // Retain the month filter

        this.loadCalendarEvents(this.filterParam);
    }

    onApplyFilter(): void {
        const filters = this.filterParam.filtering;

        // Early return if no filters are selected
        const hasSelectedFilters = Object.entries(filters).some(
            ([key, value]) => key !== 'searchText' && value !== undefined && value !== null && value !== '',
        );

        if (!hasSelectedFilters) {
            this.toastService.error('Please select at least one filter before applying.');
            return;
        }

        filters.searchTerm = undefined;

        // Call refresh after applying the filters
        this.loadCalendarEvents(this.filterParam);
    }

    onEdit(data: any): void {
        this.closeModal();

        const state = {
            step: data.step ?? 1,
            firstLoad: true,
        };

        const url = `/dashboard/shipment/edit/${data.id}`;
        const encoded = btoa(JSON.stringify(state));
        window.open(`${url}?q=${encoded}`, '_blank');
    }

    updateCalendarEvents(events: any[]) {
        const calendarApi = this.calendarComponent.getApi();

        this.ngZone.runOutsideAngular(() => {
            setTimeout(() => {
                calendarApi.batchRendering(() => {
                    calendarApi.removeAllEvents();
                    calendarApi.addEventSource(events);
                    calendarApi.refetchEvents();
                    calendarApi.updateSize();
                });
            }, 0);
        });
    }

    private getTodayEndDate(): string {
        const today = new Date();
        const year = today.getFullYear();
        const month = today.getMonth() + 1; // current month (0-based, so add 1)

        // Move to next month
        const nextMonth = new Date(year, month, 1); // 1st of next month

        const nextYear = nextMonth.getFullYear();
        const nextMonthStr = String(nextMonth.getMonth() + 1).padStart(2, '0');

        return `${nextYear}-${nextMonthStr}-01`;
    }

    private manageDynamicPopoverPosition(
        targetSelector: string,
        mouseEvent: MouseEvent | null,
        containerSelector: string,
        offsetX: number = 0,
        offsetY: number = 0,
    ): void {
        const observer = new MutationObserver(() => {
            queueMicrotask(() => {
                const popoverEl = document.querySelector(targetSelector) as HTMLElement;

                if (popoverEl && mouseEvent) {
                    observer.disconnect();

                    const container = document.querySelector(containerSelector) as HTMLElement;
                    const containerRect = container.getBoundingClientRect();

                    const clickX = mouseEvent.clientX - (containerRect.left + container.scrollLeft) - offsetX;
                    const clickY = mouseEvent.clientY - (containerRect.top + container.scrollTop) - offsetY;

                    // Positioning the element
                    popoverEl.style.position = 'absolute';
                    popoverEl.style.left = '0px';
                    popoverEl.style.top = '0px';
                    popoverEl.style.transform = `translate(${clickX}px, ${clickY}px)`;
                    popoverEl.style.zIndex = '9999';
                }
            });
        });
        observer.observe(document.body, { childList: true, subtree: true });
    }

    private getColorForStatus(status: string): string {
        switch (status?.toLowerCase()) {
            case 'new':
                return '#ec0b0b';
            case 'assigned':
                return '#007bff';
            case 'intransit':
                return '#ffc107';
            case 'delivered':
                return '#28a745';
            case 'completed':
                return '#6f42c1';
            default:
                return '#28a745'; // Fallback color
        }
    }

    // start of typeahead Search
    setupAllSearches() {
        const typeHeadFilter = new FilterParam();

        this.searchConfigs.forEach(config => {
            this.typeAheadService.setupSearchSubscription<any>(
                config.subject,
                typeHeadFilter,
                (param: FilterParam) => config.fetchFunction(param, config.endpoint),
                config.updateResults,
                config.updateLoading,
                config.selectedItemGetter,
                config.endpoint
            );
        });
    }
    // end of typeahead search
}

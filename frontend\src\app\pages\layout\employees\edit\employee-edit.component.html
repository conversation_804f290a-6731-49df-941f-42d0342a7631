<div class="site-page-container mt-3 mt-md-4">
    <div class="site-card">
        <form #employeeForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-9">
                    <div class="custom-responsive-row row">
                        <div class="label-wrap-box mb-3">
                            <span>{{ 'USERS.BasicInfo' | translate }}</span>
                        </div>

                        <!-- First Name Field -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <input
                                    class="form-control"
                                    type="text"
                                    name="firstName"
                                    #FirstName="ngModel"
                                    required="required"
                                    [(ngModel)]="employee.firstName"
                                    placeholder="{{ 'USERS.FirstName' | translate }}"
                                    [ngClass]="{ 'is-invalid': !FirstName.valid && onClickValidation }"
                                />
                                <label for="FirstName">{{ 'USERS.FirstName' | translate }}</label>
                                <app-validation-message [field]="FirstName" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Last Name field -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating mb-14">
                                <input
                                    class="form-control"
                                    type="text"
                                    name="lastName"
                                    #LastName="ngModel"
                                    [(ngModel)]="employee.lastName"
                                    placeholder="{{ 'USERS.LastName' | translate }}"
                                    required="required"
                                    [ngClass]="{ 'is-invalid': !LastName.valid && onClickValidation }"
                                />
                                <label for="LastName">{{ 'USERS.LastName' | translate }}</label>
                                <app-validation-message [field]="LastName" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Email -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <input
                                    class="form-control"
                                    type="text"
                                    name="email"
                                    #Email="ngModel"
                                    [(ngModel)]="employee.email"
                                    required="required"
                                    appEmailValidator
                                    placeholder="{{ 'USERS.Email' | translate }}"
                                    [ngClass]="{
                                        'is-invalid': !Email.valid && onClickValidation,
                                    }"
                                    [disabled]="employee.id"
                                />
                                <label for="email">{{ 'USERS.Email' | translate }}</label>
                                <app-validation-message [field]="Email" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Phone Number field -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2 mb-14">
                            <app-dial-code-input
                                [(countryCode)]="employee.countryCode"
                                [(number)]="employee.phoneNumber"
                                [required]="true"
                                [labelName]="'Address.phoneNumber'"
                                [onClickValidation]="onClickValidation"
                                fieldName="phoneNumber"
                                nameCode="countryCode"
                            ></app-dial-code-input>
                        </div>

                        <!-- Position -->
                        <div class="col-md-6 px-0 pe-md-2 ps-md-0">
                            <div class="form-group form-floating mb-14 custom-ng-select">
                                <ng-select
                                    [items]="userRoles"
                                    bindLabel="name"
                                    bindValue="id"
                                    (ngModelChange)="onRoleChange($event)"
                                    [(ngModel)]="employee.roleName"
                                    #RoleName="ngModel"
                                    [ngClass]="{
                                        'is-invalid': !RoleName.valid && onClickValidation && request.isNewRecord,
                                    }"
                                    name="roleName"
                                    required="required"
                                    [disabled]="employee.id"
                                >
                                </ng-select>
                                <app-validation-message [field]="RoleName" [onClickValidation]="onClickValidation">
                                </app-validation-message>

                                <label for="role" class="ng-select-label">
                                    {{ 'USERS.Position' | translate }}
                                </label>
                            </div>
                        </div>

                        <!-- Employment Status  -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-group form-floating mb-14 custom-ng-select">
                                <ng-select
                                    [items]="employeeStatus"
                                    bindLabel="name"
                                    bindValue="id"
                                    [(ngModel)]="employee.isActive"
                                    [clearable]="false"
                                    #EmploymentStatus="ngModel"
                                    name="employmentStatus"
                                    (click)="setActiveDatePicker(null)"
                                >
                                </ng-select>
                                <label for="branch" class="ng-select-label">{{
                                    'USERS.EmploymentStatus' | translate
                                }}</label>
                            </div>
                        </div>

                        <!-- Emergency Contact Name field -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating form-group mb-14">
                                <input
                                    class="form-control"
                                    type="text"
                                    name="emergencyContactName"
                                    #EmergencyContactName="ngModel"
                                    [(ngModel)]="employee.emergencyContactName"
                                    placeholder="{{ 'UserProfile.emergencyContactName' | translate }}"
                                />
                                <label for="EmergencyContactName">{{
                                    'UserProfile.emergencyContactName' | translate
                                }}</label>
                            </div>
                        </div>

                        <!-- Emergency Contact number field -->
                        <div class="col-md-6 px-0 mb-14 pe-md-0 ps-md-2">
                            <app-dial-code-input
                                [(countryCode)]="employee.emergencyContactCountryCode"
                                [labelName]="'UserProfile.emergencyContactNumber'"
                                [(number)]="employee.emergencyContactNumber"
                                [onClickValidation]="onClickValidation"
                                fieldName="EmergencyContact"
                            ></app-dial-code-input>
                        </div>

                        <!-- Passport Number -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating form-group mb-14">
                                <input
                                    class="form-control"
                                    type="text"
                                    name="passportNo"
                                    #PassportNo="ngModel"
                                    [maxlength]="30"
                                    [(ngModel)]="employee.passportNumber"
                                    placeholder="{{ 'UserProfile.passportNumber' | translate }}"
                                />
                                <label for="EmergencyPassportNo">{{ 'UserProfile.passportNumber' | translate }}</label>
                            </div>
                        </div>

                        <!--Driver Number -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating form-group mb-14">
                                <input
                                    class="form-control"
                                    type="text"
                                    name="driverNumber"
                                    #DriverNumber="ngModel"
                                    [(ngModel)]="employee.driverNo"
                                    [disabled]="employee.roleName !== 'ROLE_DRIVER'"
                                    [maxlength]="30"
                                    placeholder="{{ 'UserProfile.driverNumber' | translate }}"
                                />

                                <label for="DriverNumber">
                                    {{ 'UserProfile.driverNumber' | translate }}
                                </label>
                            </div>
                        </div>

                        <!--Hire Date -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2 mb-14">
                            <app-ng-custom-date-picker
                                name="hireDate"
                                ngDefaultControl
                                *ngIf="activeDatePicker"
                                [labelName]="'USERS.HireDate'"
                                [onClickValidation]="onClickValidation"
                                [selectedDate]="employee.hireDate"
                                (setDate)="setDate($event)"
                                (click)="setActiveDatePicker('hireDate')"
                                (datePickerClosed)="setActiveDatePicker(null)"
                                [customZIndex]="openDatePicker === 'hireDate' ? 'z-index-4' : 'z-index-3'"
                            ></app-ng-custom-date-picker>
                        </div>

                        <div class="label-wrap-box mt-2 mb-3">
                            <span>{{ 'Address.objName' | translate }}</span>
                        </div>

                        <!-- Address -->
                        <app-custom-address
                            [isRequired]="true"
                            [(address)]="employee.addressDetail"
                            [onClickValidation]="onClickValidation"
                        ></app-custom-address>

                        <!-- Documents Upload -->
                        <div class="col-md-12 mb-14 px-0 px-md-0">
                            <app-file-uploader
                                [uploaderId]="'employeeUploaderId'"
                                [documents]="employee.documents"
                            ></app-file-uploader>
                        </div>
                    </div>

                    <div class="clearfix"></div>
                    <div class="col-md-12 custom-buttons-container">
                        <button class="btn cancel-button" appRippleEffect type="button" (click)="handleCancelClick()">
                            {{ 'COMMON.CANCEL' | translate }}
                        </button>
                        <button
                            class="btn save-button"
                            appRippleEffect
                            type="button"
                            (click)="save(employeeForm.form, { isAddressRequired: true, isPhoneNumberRequired: true })"
                        >
                            <div class="site-button-inner">
                                {{ 'COMMON.SAVE' | translate }}
                            </div>
                        </button>
                        <div class="clearfix"></div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

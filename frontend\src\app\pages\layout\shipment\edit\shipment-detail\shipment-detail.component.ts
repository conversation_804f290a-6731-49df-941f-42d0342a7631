// Angular core
import { Component, EventEmitter, Input, Output, TemplateRef } from '@angular/core';

// Angular common and forms
import { CommonModule, NgClass } from '@angular/common';
import { FormsModule } from '@angular/forms';

// Angular router
import { ActivatedRoute, NavigationStart, Router } from '@angular/router';

// Third-party modules
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';

// RxJS
import { lastValueFrom, Subject, Subscription } from 'rxjs';

// App constants and models
import { Constant } from '../../../../../config/constants';
import { FilterParam } from '../../../../../models/common/filter-param';
import { RateSheet } from '../../../../../models/rate-sheet';
import { Shipment } from '../../../../../models/shipment/shipment';

// Shared services
import { LoadingService } from '../../../../../services/loading.service';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';
import { TypeAheadService } from '../../../../../shared/services/typeahead-search.service';

// Shared components
import { NgCustomDatePickerComponent } from '../../../../../shared/common-component/ng-custom-date-picker/ng-custom-date-picker.component';
import { NgCustomTimePickerComponent } from '../../../../../shared/common-component/ng-custom-time-picker/ng-custom-time-picker.component';
import { ValidationMessageComponent } from '../../../../../shared/common-component/validation-message/validation-message.component';

// Feature managers
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import moment from 'moment';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { Vehicle } from '../../../../../models/vehicle';
import { AllowNumberOnlyDirective } from '../../../../../shared/directives/allow-number-only.directive';
import { CustomerManager } from '../../../customer/customer.manager';
import { EmployeesManager } from '../../../employees/employees.manager';
import { RateSheetManager } from '../../../ratesheet/rate-sheet.manager';
import { VehicleEditComponent } from '../../../vehicle/edit/vehicle-edit.component';
import { VehicleManager } from '../../../vehicle/vehicle.manager';
import { ShipmentManager } from '../../shipment.manager';

import { Barcode } from '../../../../../models/barcode';
import { CommonUtil } from '../../../../../shared/common.util';

@Component({
    selector: 'app-shipment-detail',
    standalone: true,
    imports: [
        FormsModule,
        CommonModule,
        TranslateModule,
        NgClass,
        NgSelectModule,
        NgCustomDatePickerComponent,
        NgCustomTimePickerComponent,
        ValidationMessageComponent,
        AllowNumberOnlyDirective,
        NgxMaskDirective,
        VehicleEditComponent,
    ],
    templateUrl: './shipment-detail.component.html',
    styleUrl: './shipment-detail.component.scss',
    providers: [provideNgxMask()],
})
export class ShipmentDetailComponent {
    @Input() shipment!: Shipment;
    @Input() onClickValidation!: boolean;
    @Input() request!: any;
    @Input() filterParam!: FilterParam;
    @Input() intialRateSheetValue!: string | null;
    @Input() disabled!: boolean;

    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();
    @Output() onfetchDropdownDataCompleted = new EventEmitter<boolean>();

    private delayInputTimer: any;

    searchRateSheetSubject: Subject<string> = new Subject<string>();
    searchVehicleSubject: Subject<string> = new Subject<string>();

    readonly rateSheetFetchFn = this.rateSheetManager.fetchForDropdownData.bind(this.rateSheetManager);
    readonly vehicleFetchFn = this.vehicleManager.fetchForDropdownData.bind(this.vehicleManager);

    loadingRateSheetNgSelect!: boolean;
    loadingVehicleNgSelect!: boolean;

    hasTriggered: boolean = false;

    routerSubscription?: Subscription;
    title!: string;

    searchConfigs = [
        {
            subject: this.searchRateSheetSubject,
            fetchFunction: this.rateSheetFetchFn,
            updateResults: (results: any) => (this.rateSheets = results),
            updateLoading: (isLoading: boolean) => (this.loadingRateSheetNgSelect = isLoading),
        },
        {
            subject: this.searchVehicleSubject,
            fetchFunction: this.vehicleFetchFn,
            updateResults: (results: any) => (this.vehicles = results),
            updateLoading: (isLoading: boolean) => (this.loadingVehicleNgSelect = isLoading),
        },
    ];

    showToEndDateField: boolean = true;
    updateToEndDateField: boolean = true;

    shipmentPaymentStatusOptions = Constant.SHIPMENT_PAYMENT_STATUS_OPTIONS;
    shipmentStatusOptions = Constant.SHIPMENT_STATUS_OPTIONS;
    shipmentTypeOptions = Constant.SHIPMENT_TYPE_OPTIONS;
    shipmentPaymentTypeOptions = Constant.SHIPMENT_PAYMENT_TYPE_OPTIONS;

    rateSheets: RateSheet[] = [];
    vehicles: Vehicle[] = [];
    barcodes: Barcode[] = [];

    vehicleModelRef!: NgbModalRef;

    openDatePicker: 'startDate' | 'endDate' | 'etd' | null = null;

    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected loadingService: LoadingService,
        protected route: ActivatedRoute,
        protected router: Router,
        protected customerManager: CustomerManager,
        protected shipmentManager: ShipmentManager,
        protected vehicleManager: VehicleManager,
        protected employeesManager: EmployeesManager,
        protected rateSheetManager: RateSheetManager,
        protected typeAheadService: TypeAheadService,
        protected modalService: NgbModal,
    ) { }

    ngAfterViewInit(): void {
        this.setupAllSearches();
    }

    getFormattedDate(date: any) {
        return moment(date).format('YYYY-MM-DD')
    }

    ngOnInit(): void {
        this.fetchDropdownData();
        if (this.request.recordId === '0') {
            this.shipmentManager.fetchShipmentRefId().subscribe({
                next: (refId) => {
                    if (refId) {
                        this.shipment.refID = refId;
                    }
                },
            });
        }
        this.onfetchDropdownDataCompleted.emit(false);
        this.routerSubscription = this.router.events.subscribe((event) => {
            if (event instanceof NavigationStart && this.vehicleModelRef) {
                this.vehicleModelRef.close();
            }
        });
    }

    ngOnDestroy(): void {
        if (this.routerSubscription) {
            this.routerSubscription.unsubscribe();
        }
    }

    handleCancelClick() {
        this.router.navigate(['/dashboard/shipments']);
    }

    async ngDoCheck(): Promise<void> {
        if (!this.hasTriggered && this.shipment?.customer) {
            this.hasTriggered = true;
            this.addObjectToExistingDropdown();
        }
    }

    async fetchDropdownData() {
        this.loadingService.show();

        this.filterParam.pagination.next = 10;
        this.filterParam.pagination.offset = 1;

        const rateSheetParam = new FilterParam();
        rateSheetParam.pagination.next = 10;
        rateSheetParam.pagination.offset = 1;
        rateSheetParam.filtering.date = this.shipment.createdOn;

        const [rateSheets, vehicles] = await Promise.all([
            lastValueFrom(this.rateSheetManager.fetchForDropdownData(rateSheetParam)),
            lastValueFrom(this.vehicleManager.fetchForDropdownData(this.filterParam)),
        ]);

        this.rateSheets = rateSheets;
        this.vehicles = vehicles;
        this.addObjectToExistingDropdown();
        this.loadingService.hide();
        this.onfetchDropdownDataCompleted.emit(true);
    }

    onKeyUpLoadingDelay(): void {
        clearTimeout(this.delayInputTimer);
        this.delayInputTimer = setTimeout(() => {
            this.shipment.loadingDelay = CommonUtil.formatDecimalMinutes(this.shipment.loadingDelay);
        }, 600);
    }

    setActiveDatePicker(picker: 'startDate' | 'endDate' | 'etd' | null) {
        this.openDatePicker = picker;
    }

    setStartDate(selectedDate: string | null) {
        this.shipment.startDate = selectedDate;

        this.updateToEndDateField = false;
        this.showToEndDateField = true;

        setTimeout(() => {
            if (this.shipment.endDate) {
                this.shipment.endDate = null;
            }

            this.updateToEndDateField = true;
        });
    }

    setEndDate(selectedDate: string | null, dateType: string) {
        if (dateType === 'ETD') {
            this.shipment.etd = selectedDate;
        } else {
            this.shipment.endDate = selectedDate;
        }
    }

    onRateSheetChange(data: any): void {
        if (this.intialRateSheetValue !== data?.id && this.shipment.isCargoAdded) {
            this.commonService.confirmation(
                'Would you like to change the shipment calculations?',
                this.rateSheetChangeCallBack.bind(this),
            );
        }
    }

    rateSheetChangeCallBack(): void {
        this.shipment.isRateSheetChanged = true;
    }

    getCustomerDetails({ customerDetail }: any): void {
        if (!customerDetail) return;

        const { keyContact, keyContactEmail, keyContactPhone } = customerDetail;

        Object.assign(this.shipment, {
            contactPersonName: keyContact,
            contactPersonEmail: keyContactEmail,
            contactPersonPhone: keyContactPhone,
        });
    }

    addUniqueItem(list: any[] = [], item: any) {
        if (item?.id && !this.isDuplicate(list, item)) {
            return [...list, item];
        }
        return list;
    }

    isDuplicate(array: any, item: any) {
        return array?.some((existingItem: { id: any }) => existingItem.id === item.id);
    }

    addObjectToExistingDropdown() {
        const { rateSheetDetail, vehicleDetail, barcodeDetail } = this.shipment;

        this.rateSheets = this.addUniqueItem(this.rateSheets, rateSheetDetail);
        this.vehicles = this.addUniqueItem(this.vehicles, vehicleDetail);
        this.barcodes = this.addUniqueItem(this.barcodes, barcodeDetail);
    }

    openModal(content: TemplateRef<any>, title: string, isCertificateTypeModal: boolean): void {
        this.vehicleModelRef = this.modalService.open(content, { centered: true, backdrop: 'static' });
        this.title = title;
    }

    async getSavedVehicle(newData: Vehicle): Promise<void> {
        if (newData) {
            this.vehicles = [...this.vehicles, newData];
        }
        this.shipment.vehicle = newData?.id;
        this.vehicleModelRef?.close();
    }

    onNext(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }

        this.onNextClick.emit(4);
    }

    save(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }

        this.saveButtonClicked.emit();
    }

    onBack() {
        this.onNextOrBackClick.emit(2);
    }

    setTodayDate(): string {
        return moment().format('YYYY-MM-DD');
    }

    // start of typeahead Search
    setupAllSearches() {
        this.searchConfigs.forEach((config) => {
            this.typeAheadService.setupSearchSubscription(
                config.subject,
                new FilterParam(),
                (filterParam) => {
                    if (config.fetchFunction === this.rateSheetFetchFn) {
                        filterParam.filtering.date = this.setTodayDate() || null;
                    }
                    return lastValueFrom(config.fetchFunction(filterParam));
                },
                config.updateResults,
                config.updateLoading,
            );
        });
    }
}

// Angular core imports
import { Directive, Input, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';

// Third-party library imports
import { DataTableDirective } from 'angular-datatables';
import { ADTColumns, ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';

// Project-specific model imports
import { BaseModel } from './base.model';

// Project-specific service imports
import { LoadingService } from '../services/loading.service';
import { CommonService } from '../shared/services/common.service';
import { ToastService } from '../shared/services/toast.service';

// Project-specific manager imports
import { BaseManager } from './base.manager';

// Project-specific component imports
import { RestResponse } from '../models/common/auth.model';
import { FilterParam, Pagination } from '../models/common/filter-param';
import { BaseComponent } from './base.component';
import { Constant } from './constants';

@Directive()
export class BaseListServerSideComponent extends BaseComponent {
    @Input() filterParam: FilterParam = new FilterParam();
    @Input() isDetailPage!: boolean;
    @Input() onAssociatedValueSelected?: (data: any, name: string) => void;

    @ViewChild(DataTableDirective, { static: false })
    dtElement!: DataTableDirective;

    dtOptions: ADTSettings = {
        order: [],
        pagingType: 'full_numbers',
        pageLength: 10,
        serverSide: true,
        searching: false,
        lengthChange: false,
        ordering: true,
        initComplete: () => {
            this.onTableRendered();
        },
        language: {
            search: '',
            searchPlaceholder: 'Search...',
            paginate: {
                previous: 'Prev',
                next: 'Next',
            },
        },
        ajax: (dataTablesParameters: any, dataTableCallback: any) => {
            this.fetchRecords(dataTablesParameters, dataTableCallback);
        },
        columns: [],
    };

    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();

    columnOrders: string[] = [];
    records: BaseModel[] = [];
    isPlusButton?: boolean;
    hasDataLoad: boolean = true;
    selectedId!: string;

    constructor(
        protected override manager: BaseManager,
        protected override commonService: CommonService,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override router: Router,
    ) {
        super(manager, commonService, toastService, loadingService, router);
    }

    // this method will removed once we remove all the existing component in which it used
    initDataTableColumn(
        input: TemplateRef<any>,
        defaultContent: any = '',
        data: string,
        className: string | null = null,
    ): ADTColumns {
        return {
            data: data,
            defaultContent: defaultContent,
            className: className,
            ngTemplateRef: {
                ref: input,
            },
        } as ADTColumns;
    }

    ngOnDestroy(): void {
        this.dtTrigger.unsubscribe();
    }

    // Public Methods
    override init(): void {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 100);
    }

    clearSearch(searchInput?: HTMLInputElement): void {
        if (searchInput) {
            searchInput.value = '';
        }
    }

    async exportToExcel(
        param: FilterParam,
        customURL?: string,
        isRefreshRecord?: boolean,
        isBarCode?: boolean,
    ): Promise<void> {
        const contentType = Constant.EXPORT_TO_EXCEL.CONTENT_TYPE;

        this.loadingService.show();
        if (!isBarCode) {
            param.pagination.next = param.pagination.offset = undefined;
        }
        param.timeZoneOffset = this.commonService.getTimeZoneInfo().timeZoneOffset;
        this.manager.downloadExcelFile(param, customURL).subscribe({
            next: (response: RestResponse) => {
                if (!response.data) {
                    this.loadingService.hide();
                    this.toastService.success(response.message);
                }
                if (response.data) {
                    const blob = new Blob(
                        [
                            new Uint8Array(
                                atob(response.data)
                                    .split('')
                                    .map((content) => content.charCodeAt(0)),
                            ),
                        ],
                        { type: contentType },
                    );
                    const url = window.URL.createObjectURL(blob);
                    const anchorTag = document.createElement('a');
                    anchorTag.href = url;
                    anchorTag.download = param.fileName;
                    document.body.appendChild(anchorTag);
                    anchorTag.click();
                    window.URL.revokeObjectURL(url);
                    anchorTag.remove();

                    this.loadingService.hide();
                    if (isRefreshRecord) {
                        this.refreshRecord();
                    }
                }
            },
            error: (error) => {
                this.loadingService.hide();
                this.toastService.error(error.message);
            },
        });
    }

    async exportToPDF(id: string): Promise<void> {
        const param = this.filterParam;

        this.loadingService.show();
        param.pagination.next = this.filterParam.pagination.offset = undefined;
        param.timeZoneOffset = this.commonService.getTimeZoneInfo().timeZoneOffset;

        const contentType = Constant.EXPORT_TO_PDF.CONTENT_TYPE;

        this.manager.downloadPDfFile(this.filterParam, id).subscribe({
            next: (response: RestResponse) => {
                const blob = new Blob(
                    [
                        new Uint8Array(
                            atob(response.data[0].base64String)
                                .split('')
                                .map((char) => char.charCodeAt(0)),
                        ),
                    ],
                    { type: contentType },
                );
                const url = window.URL.createObjectURL(blob);
                const anchorTag = document.createElement('a');
                anchorTag.href = url;
                anchorTag.download = response.data[0].fileName;
                document.body.appendChild(anchorTag);
                anchorTag.click();
                window.URL.revokeObjectURL(url);
                anchorTag.remove();

                this.loadingService.hide();
            },
        });
    }

    onClearFilter(searchInput?: HTMLInputElement, fieldsNotCleared: string[] = []) {
        const filters = this.filterParam.filtering;

        const hasFilters = Object.values(filters).some((value) => value !== undefined);
        if (!hasFilters) {
            return;
        }

        // Set the filter values to 'undefined' for any keys that are not included in the 'fieldsNotCleared' array.
        // This effectively clears the filter values for those fields.
        Object.keys(filters).forEach((key) => {
            if (!fieldsNotCleared.includes(key)) {
                (filters as Record<string, any>)[key] = undefined;
            }
        });

        // This function clears the 'searchText' input field on the current page and resets any search-related filters.
        if (searchInput) {
            this.clearSearch(searchInput);
        }

        this.refreshRecord(true);
    }

    onApplyFilter(): void {
        const filters = this.filterParam.filtering;
        // Early return if no filters are selected
        const hasSelectedFilters = Object.entries(filters).some(
            ([key, value]) => key !== 'searchText' && value !== undefined && value !== null && value !== '',
        );
        if (!hasSelectedFilters) {
            this.toastService.error('Please select at least one filter before applying.');
            return;
        }

        filters.searchTerm = undefined;

        // Convert filters: status and contractor fields to boolean or undefined
        filters.isActive = this.convertStatusToBoolean(filters.status, 'ACTIVE', 'INACTIVE');

        // Call refresh after applying the filters
        this.refreshRecord(true);
    }

    onFetchCompleted(): void {
        this.hasDataLoad = true;
    }

    refreshRecord(resetToFirstPage: boolean = false): void {
        this.dtElement.dtInstance.then((dtInstance: any) => {
            dtInstance.ajax.reload(null, resetToFirstPage);
        });
    }

    search(value: string): void {
        this.filterParam.filtering.searchText = value.trim();
        this.refreshRecord();
    }

    override clean(): void {
        super.clean();
        this.dtTrigger.unsubscribe();
    }

    override removeSuccess(): void {
        this.refreshRecord();
    }

    // Protected Methods
    protected createColumnConfig(template: TemplateRef<any>, orderable: boolean = true): any {
        return {
            orderSequence: Constant.ORDER_SEQUENCE,
            data: null,
            defaultContent: '',
            ngTemplateRef: { ref: template },
            orderable: orderable,
        };
    }

    protected setupColumns(
        templates: { [key: string]: TemplateRef<any> },
        sortableColumns: boolean = true,
        nonSortableKeys: string[] = [],
    ): void {
        const allNonSortableKeys = ['action', ...nonSortableKeys];

        this.dtOptions.columns = Object.keys(templates).map((key) =>
            this.createColumnConfig(templates[key], allNonSortableKeys.includes(key) ? false : sortableColumns),
        );
    }

    protected async fetchRecords(param: any, callBack: any): Promise<void> {
        this.hasDataLoad = false;
        this.setParam(param);
        this.loadingService.show();

        this.manager.fetchAll(this.filterParam).subscribe({
            next: (response) => {
                this.loadingService.hide();

                this.records = response.data;
                setTimeout(() => {
                    callBack({
                        recordsTotal: this.records.length > 0 ? this.records[0].totalCount : this.records.length,
                        recordsFiltered: this.records.length > 0 ? this.records[0].totalCount : this.records.length,
                        data: this.records,
                    });
                    this.hasDataLoad = true;
                    this.onFetchCompleted();
                }, 100);
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }

    // Helper function to convert status-like filters into booleans
    private convertStatusToBoolean = (
        value: string | undefined,
        trueLabel: string,
        falseLabel: string,
    ): boolean | undefined => {
        if (value === trueLabel) return true;
        if (value === falseLabel) return false;
        return undefined;
    };

    // Private Methods
    private setParam(dataTablesParameters: any): void {
        const { start, length, search, order } = dataTablesParameters;

        dataTablesParameters.offset = Math.ceil(start / length) + 1;
        dataTablesParameters.next = length;

        if (this.dtOptions.searching) {
            dataTablesParameters.searchText = search?.value?.trim() || null;
        }

        if (order.length > 0) {
            const { column, dir } = order[0];

            this.filterParam.sorting = {
                sortColumn: this.columnOrders[column],
                sortOrder: dir,
            };
        }

        // Initialize pagination if it's not already defined
        if (!this.filterParam.pagination) {
            this.filterParam.pagination = new Pagination();
        }

        // Assign the pagination values'
        if (length > 0) {
            this.filterParam.pagination.offset = dataTablesParameters.offset;
            this.filterParam.pagination.next = dataTablesParameters.next;
        }
    }

    onTableRendered() { }
}

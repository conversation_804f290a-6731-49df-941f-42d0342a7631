import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { BaseManager } from '../../../config/base.manager';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';
import { LoadingService } from '../../../services/loading.service';
import { ToastService } from '../../../shared/services/toast.service';
import { NotificationsService } from './notifications.service';

@Injectable({
    providedIn: 'root',
})
export class NotificationsManager extends BaseManager {
    constructor(
        protected notificationsService: NotificationsService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(notificationsService, loadingService, toastService);
    }

    fetchNotificationCount(filterParam: FilterParam): Observable<RestResponse> {
        return this.notificationsService
            .getNotificationCount(filterParam)
            .pipe(map((response: RestResponse) => response));
    }

    getNotifications(filterParam: FilterParam): Observable<RestResponse> {
        return this.notificationsService.fetchAll(filterParam).pipe(map((response: RestResponse) => response));
    }

    notificationAction(notifications: any, isDeleteAction?: boolean): Observable<RestResponse> {
        return this.notificationsService.notificationAction(notifications, isDeleteAction);
    }

    notificationActions(isDeleteAction?: boolean): Observable<RestResponse> {
        return this.notificationsService.notificationAllAction(isDeleteAction);
    }
}

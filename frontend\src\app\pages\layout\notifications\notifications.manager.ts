import { Injectable } from '@angular/core';
import { BehaviorSubject, map, Observable, tap } from 'rxjs';
import { BaseManager } from '../../../config/base.manager';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';
import { LoadingService } from '../../../services/loading.service';
import { ToastService } from '../../../shared/services/toast.service';
import { NotificationsService } from './notifications.service';

@Injectable({
    providedIn: 'root',
})
export class NotificationsManager extends BaseManager {
    private _count$ = new BehaviorSubject<number>(0);
    public count$ = this._count$.asObservable();

    constructor(
        protected notificationsService: NotificationsService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(notificationsService, loadingService, toastService);
    }

    ngOnInit() {
        this.updateCount(0);
    }

    updateCount(count: number) {
        console.log('count', count);
        this._count$.next(count);
        console.log("akhgdaSKDJAHD", this._count$.value);
    }

    fetchNotificationCount(filterParam: FilterParam): Observable<RestResponse> {
        return this.notificationsService.getNotificationCount(filterParam).pipe(
            tap((res: RestResponse) => {
                this._count$.next(res.data?.totalCount ?? 0); // push count into BehaviorSubject
            })
        );
    }
    // Allow direct set (if needed locally)
    setCount(count: number) {
        this._count$.next(count);
    }
    getNotifications(filterParam: FilterParam): Observable<RestResponse> {
        return this.notificationsService.fetchAll(filterParam).pipe(map((response: RestResponse) => response));
    }

    notificationAction(notifications: any, isDeleteAction?: boolean): Observable<RestResponse> {
        return this.notificationsService.notificationAction(notifications, isDeleteAction);
    }

    notificationActions(isDeleteAction?: boolean): Observable<RestResponse> {
        return this.notificationsService.notificationAllAction(isDeleteAction);
    }
    refresh
}

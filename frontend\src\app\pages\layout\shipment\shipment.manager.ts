// Angular core
import { Injectable } from '@angular/core';

// RxJS
import { catchError, map, Observable, of } from 'rxjs';

// App configuration and base classes
import { BaseManager } from '../../../config/base.manager';

// Models
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';

// Services
import { LoadingService } from '../../../services/loading.service';
import { ToastService } from '../../../shared/services/toast.service';
import { ShipmentService } from './shipment.service';
@Injectable({
    providedIn: 'root',
})
export class ShipmentManager extends BaseManager {
    stepperList: any[] = [
        {
            stepNumber: 1,
            stepTitle: 'Basic Details',
            stepPath: '/assets/images/icons/information-sign.svg',
            selected: true,
        },
        {
            stepNumber: 2,
            stepTitle: 'Pickup Delivery',
            stepPath: '/assets/images/icons/delivery_truck_speed.svg',
            selected: false,
        },
        {
            stepNumber: 3,
            stepTitle: 'Shipment Details',
            stepPath: '/assets/images/icons/Shipment_Detail_icon.svg',
            selected: false,
        },
        {
            stepNumber: 4,
            stepTitle: 'Cargo Details',
            stepPath: '/assets/images/icons/Cargo_detail_icon.svg',
            selected: false,
        },
        {
            stepNumber: 5,
            stepTitle: 'Special Request',
            stepPath: '/assets/images/svg/Special-request-icon.svg',
            selected: true,
        },
        {
            stepNumber: 6,
            stepTitle: 'Calculations',
            stepPath: '/assets/images/svg/Calculation_icon.svg',
            selected: false,
        },
        {
            stepNumber: 7,
            stepTitle: 'Documents',
            stepPath: '/assets/images/icons/file.svg',
            selected: false,
        },
        {
            stepNumber: 8,
            stepTitle: 'POD Delivery',
            stepPath: '/assets/images/svg/Pod_icon.svg',
            selected: false,
        },
    ];

    constructor(
        protected shipmentService: ShipmentService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(shipmentService, loadingService, toastService);
    }

    fetchForPickupCityDropdown(param: FilterParam) {
        return this.shipmentService.fetchForPickupCityDropdown(param).pipe(
            map((response: RestResponse) => {
                return response.data;
            }),
            catchError((error) => {
                const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
                this.toastService.error(errorMessage);
                return of([]);
            }),
        );
    }

    fetchShipmentRefId(): Observable<string | null> {
        return this.shipmentService
            .fetchShipmentRefId()
            .pipe(map((response: RestResponse) => response?.data?.refID ?? null));
    }

    fetchSpecialPrice(param: FilterParam): Observable<string | null> {
        return this.shipmentService
            .fetchSpecialPrice(param)
            .pipe(map((response: RestResponse) => response?.data ?? null));
    }

    updateShipmentStatus(data: any): Observable<RestResponse> {
        return this.shipmentService.updateShipmentStatus(data).pipe(map((response: RestResponse) => response));
    }

    fetchShipmentCalenderDetails(filterParam: FilterParam): Observable<RestResponse> {
        return this.shipmentService
            .fetchShipmentCalenderDetails(filterParam)
            .pipe(map((response: RestResponse) => response));
    }

    invoiceShipment(rowData: any[]): Observable<RestResponse> {
        return this.shipmentService.invoiceShipment(rowData).pipe(
            map((response: RestResponse) => response)
        );
    }
}

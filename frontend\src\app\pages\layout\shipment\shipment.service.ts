import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

// services
import { BaseService } from '../../../config/base.service';

// models
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';

@Injectable({
    providedIn: 'root',
})
export class ShipmentService extends BaseService {
    constructor(public override http: HttpClient) {
        super(http, '/api/shipment', '/api/shipments');
    }

    fetchForPickupCityDropdown(filterParam: FilterParam): Observable<RestResponse> {
        return this.getRecords('/api/address/city/selection', filterParam);
    }

    fetchShipmentRefId(): Observable<RestResponse> {
        return this.getRecord('/api/shipment/refId');
    }

    fetchSpecialPrice(filterParam: FilterParam): Observable<RestResponse> {
        return this.getRecords('/api/ratesheet/special/charges', filterParam);
    }

    updateShipmentStatus(data: any): Observable<RestResponse> {
        return this.saveRecord(`/api/update/shipment/status`, data);
    }

    fetchShipmentCalenderDetails(filterParam: FilterParam): Observable<RestResponse> {
        return this.getRecords('/api/shipments/calender', filterParam);
    }

    invoiceShipment(rowData: any): Observable<RestResponse> {
        return this.saveRecord('/api/quickbooks/shipments/invoice', rowData);
    }
}

{"ENGLISH": "English", "CATEGORIES": "Categories", "ACCOUNT SETTINGS": "Account settings", "SETTINGS": "Settings", "FIRST_NAME": "First name", "LAST_NAME": "Last name", "EMAIL": "Email", "MOBILE NO": "Mobile", "SAVE": "Save", "CHANGE PASSWORD": "Change password", "OLD PASSWORD": "Current password", "NEW PASSWORD": "New password", "CONFIRM NEW PASSWORD": "Confirm new password", "RECOVER PASSWORD": "Reset", "PASSWORD": "Password", "CONFIRM PASSWORD": "Confirm password", "SIGN IN": "Log In", "STAY SIGNED IN": "Stay signed in", "FORGOT PASSWORD": "Forgot password", "RESET": "Reset", "BACK TO LOGIN": "Back to login", "ADMIN": "ADMIN", "WELCOME": "Welcome", "TO": "to", "LOGIN": {"objName": "<PERSON><PERSON>", "FORGOT_PASSWORD": "Forgot password", "SIGN_IN": "Log In", "BACK_TO_LOGIN": "Back to login", "RECOVER_PASSWORD": "Recover Password", "logout": "Logout", "projectName": "Bees Express", "allRightReserved": "All rights reserved", "TermsOfUse": "Terms of Use", "PrivacyPolicy": "Privacy Policy", "PreviousPage": "Previous Page"}, "PROFILE": {"objName": "Profile", "Basic_information": "Basic Information", "change_password": "Change Password"}, "DASHBOARD": {"objName": "Dashboard", "ACCOUNT_SETTINGS": "Profile", "CHANGE_PASSWORD": "Change Password", "Contact": "Contact", "profile": "Profile", "TotalShipment": "Total Shipment", "Pending": "Pending", "Delivered": "Delivered", "Cancelled": "Cancelled", "welcome": "Welcome to Dashboard", "subtitle": "Your logistics management overview", "totalShipments": "Total Shipments", "activeQuotations": "Active Quotations", "pendingDeliveries": "Pending Deliveries", "totalRevenue": "Total Revenue", "shipmentStatus": "Shipment Status Overview", "quotationStatus": "Quotation Status Overview", "noDataTitle": "No Data Available", "noDataDescription": "There are no shipments or quotations to display at the moment."}, "EDIT": {"BASIC_INFO": "Basic Info", "ACTIVE": "Active", "ADD_NEW_RECORD": "Add New Record"}, "COMMON": {"CSV": "CSV", "ADD": "Add", "NEW": "New", "UPDATE": "Update", "EDIT": "Edit", "SAVE": "Save", "SAVEANDNEXT": "Save & Next", "ADDNEW": "Add New", "FILTER": "Filter", "DELETE": "Delete", "HOME": "Home", "NAME": "Name", "CANCEL": "Cancel", "NEXT": "Next", "CREATED_ON": "Created On", "SELECT": "Select", "REPORT": "Report", "DETAIL": "Detail", "EXCELREPORT": "Export as EXCEL", "PDFREPORT": "Export as PDF", "PDF": "PDF", "PRINTREPORT": "Print Report", "NORECORDS": "No Records Available", "ACTION": "Action", "YES": "Yes", "NO": "No", "BACK": "Back", "createdOn": "Created On", "UPLOAD_ALL": "Upload All", "STATUS": "Status", "APPLY": "Apply", "CLEAR": "Clear", "inInches": "(in Inches)", "cubicInches": "(in Cubic Inches)", "inDollar": "(in $)", "inKm": "(in KM)", "inLbs": "(in Lbs)", "inLtr": "(in Ltr.)", "signature": "Signature", "Information": "Information", "AllowedFileTypes": "File must be {{types}}.", "BasicDetails": "Basic Details", "customerDetail": "Customer Details", "REQUIRED_SELECT_VALIDATION_MESSAGE": "Please select a valid value.", "REQUIRED_INPUT_VALIDATION_MESSAGE": "Please provide a valid value.", "REQUIRED_EMAIL_VALIDATION_MESSAGE": "Please provide a valid email address.", "REQUIRED_MOBILE_VALIDATION_MESSAGE": "Please provide a valid mobile.", "REQUIRED_PATTERN_VALIDATION_MESSAGE": "Please provide a valid value.", "REQUIRED_FILE_VALIDATION_MESSAGE": "Please upload a file", "REQUIRED_INPUT_MIN_VALIDATION_MESSAGE": "Please provide a valid value. Min allowed value: ", "REQUIRED_INPUT_MAX_VALIDATION_MESSAGE": "Please provide a valid value. Max allowed value: ", "REQUIRED_MASK_VALIDATION_MESSAGE": "Please provide a valid value. Value will be like: ", "YEAR_INPUT_VALIDATION_MESSAGE": "Please provide a valid year value.", "MONTH_INPUT_VALIDATION_MESSAGE": "Please provide a valid month value.", "REQUIRED_PASSWORD_COMBINATION": "Password must contain at least one uppercase letter, one lowercase letter, one number, one special character, and be at least 8 characters"}, "USERS": {"objNames": "Employees", "ADD_NEW_USERS": "Add New User", "NEW_USER": "New User", "Name": "Name", "FirstName": "First Name", "LastName": "Last Name", "Email": "Email", "Role": "Role", "SelectRole": "Select Role", "PhoneNumber": "Phone Number", "Active": "Active", "Action": "Action", "YES": "Yes", "NO": "No", "Position": "Position", "HireDate": "Hire Date", "EmploymentStatus": "Employment Status", "BasicInfo": "Basic Information", "Information": "Details"}, "CUSTOMER": {"objName": "Customer", "objNames": "Customers", "Detail": "Customer Detail", "ADD_NEW_CUSTOMER": "Add New Customer", "user": "User", "Address": "Address", "AddressInfo": "Address Information", "alternateEmail": "Alternate Email", "website": "Website", "abn": "ABN", "companyName": "Company Name", "companyPhone": "Company Phone", "PhoneNumber": "Phone Number", "keyContact": "Key Contact", "keyContactInfo": "Key Contacts Information", "keyContactName": "Key Contact Name", "keyContactPosition": "Key Contact Position", "keyContactPhone": "Key Contact Phone", "keyContactEmail": "Key Contact Email", "executiveContact": "Executive Contact", "executivePosition": "Executive Position", "executivePhone": "Executive Phone", "executiveEmail": "Executive <PERSON><PERSON>", "accountsInfo": "Accounts Information", "accountsContact": "Accounts Contact Name", "accountsPosition": "Accounts Position", "accountsPhone": "Accounts Phone", "accountsEmail": "Accounts Email", "operationsContact": "Operations Contact", "operationsPosition": "Operations Position", "tradeRefCompanyName": "Trade Ref Company Name", "tradeRefContact": "Trade Ref Contact", "tradeRefPhone": "Trade Ref Phone", "tradeRefEmail": "Trade Ref Email", "documents": "Documents", "messageDocumentsInvalid": "Please Upload At least 1 Documents", "profileImage": "ProfileImage", "messageProfileImageInvalid": "Please Upload At least 1 ProfileImage", "operationsEmail": "Operations Email", "operationsPhone": "Operations Phone", "accountsPayableEmail": "Accounts Payable Email"}, "CustomerShipments": {"shipmentPOD": "Shipment POD", "shipmentDate": "Shipment Date", "pickupAddress": "Pickup Address", "deliveryAddress": "Delivery Address", "trackingStatus": "Tracking Status"}, "RATE_SHEET": {"objName#": "Rate Sheet #", "objName": "Rate Sheet", "objNames": "Rate Sheets", "Detail": "Rate Sheet Detail", "ADD_NEW_RATESHEET": "Add New Rate Sheet", "startDate": "Start Date", "endDate": "End Date", "oversizeRate": "Oversize Rate", "rushRequestRate": "Rush Request Rate", "enclosedRate": "Enclosed Rate", "fragileRate": "Fragile Rate", "perishableRate": "Perishable Rate", "dangerousGoodsRate": "Dangerous Goods Rate", "pickupAddress": "Pickup Address", "deliveryAddress": "Delivery Address", "startDateEndDate": "Start Date - End Date", "pickupCity": "Pickup City", "deliveryCity": "Delivery City", "SpecialRequestInfo": "Special Request Information", "pickupAddressInfo": "Pickup Address Information", "deliveryAddressInfo": "Delivery Address Information"}, "RateSheetWeightCharge": {"objName": "Rate Sheet Weight Charge", "objNames": "Rate Sheet Weight Charges", "Detail": "Rate Sheet Weight Charge Detail", "ADD_NEW_RATESHEETWEIGHTCHARGE": "Add New Rate Sheet Weight Charge", "rateSheet": "Rate Sheet", "fromWeight": "From Weight (LBS)", "toWeight": "To Weight (LBS)", "freight": "Freight", "fuelCharges": "Fuel Charges", "rateType": "Rate Type", "gst": "GST Amount", "total": "Total", "InDollars": "(in $)"}, "Quotation": {"objName": "Quotation", "objNames": "Quotations", "Detail": "Quotation Detail", "ADD_NEW_QUOTATION": "Add New Quotation", "customer": "Customer", "quotationDate": "Quotation Date", "contactPersonName": "Contact Person Name", "contactPhone": "Contact Phone", "contactPersonEmail": "Contact Person Email", "summary": "Summary", "pickupCompanyName": "Pickup Company Name", "pickupContactPersonName": "Pickup Contact Person Name", "pickupContactPersonPhone": "Pickup Contact Person Phone", "deliveryCompanyName": "Delivery Company Name", "deliveryContactPersonName": "Delivery Contact Person Name", "deliveryContactPersonPhone": "Delivery Contact Person Phone", "isOversize": "Oversize", "isRushRequest": "Rush Request", "isEnclosed": "Enclosed", "isFragile": "<PERSON><PERSON><PERSON>", "isPerishable": "Perishable", "isDangerousGoods": "Dangerous Goods", "totalItems": "Total Items", "totalWeight": "Total Weight", "totalVolume": "Total Volume", "subTotal": "Sub Total", "specialTotal": "SpecialTotal", "fuelChargesTotal": "Fuel Charges Total", "gstTotal": "Gst Total", "grandTotal": "Grand Total", "STATUS_NEW": "NEW", "STATUS_PROPOSED": "PROPOSED", "STATUS_ACCEPTED": "ACCEPTED", "status": "Status", "user": "User", "pickupAddress": "Pickup Address", "deliveryAddress": "Delivery Address", "contactPersonPhone": "Contact Person Phone", "Cargo": "Cargo"}, "QuotationItem": {"objName": "Quotation Item", "objNames": "Quotation Items", "Detail": "Quotation Item Detail", "ADD_NEW_QUOTATIONITEM": "Add New Quotation Item", "quotation": "Quotation", "description": "Description", "CARGOTYPE_BOX": "BOX", "CARGOTYPE_PALLET": "PALLET", "CARGOTYPE_CRATE": "CRATE", "CARGOTYPE_ENVELOPE": "ENVELOPE", "CARGOTYPE_TUBE": "TUBE", "CARGOTYPE_DRUM": "DRUM", "CARGOTYPE_BAG": "BAG", "CARGOTYPE_SACK": "SACK", "CARGOTYPE_ROLL": "ROLL", "CARGOTYPE_BUNDLE": "BUNDLE", "CARGOTYPE_CASE": "CASE", "CARGOTYPE_BIN": "BIN", "CARGOTYPE_TOTE": "TOTE", "CARGOTYPE_CONTAINER": "CONTAINER", "CARGOTYPE_CAGE": "CAGE", "CARGOTYPE_TRAY": "TRAY", "CARGOTYPE_CART": "CART", "cargoType": "Cargo Type", "weight": "Weight", "WEIGHTTYPE_POUNDS": "POUNDS", "WEIGHTTYPE_KGS": "KGS", "weightType": "Weight Type", "weightInPounds": "Weight In Pounds", "length": "Length", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "volume": "Volume", "gstAmount": "GST Amount", "rateType": "Rate Type"}, "BARCODE": {"objName": "Barcode", "objNames": "Barcodes", "Detail": "Barcode Detail", "ADD_NEW_BARCODE": "Add New Barcode", "refID": "RefID", "driver": "Driver", "barcodeNo": "Barcode No.", "isUsed": "Used", "shimpentRefId": "Shipment Ref ID", "barCodeNumbers": "Barcodes Count", "barCodePrintStatus": "Print Status", "fromBarcode": "From Barcode", "toBarcode": "To Barcode"}, "DashboardFilter": {"30Days": "30 Days", "60Days": "60 Days", "90Days": "90 Days", "180Days": "180 Days", "1Year": "1 Year", "all": "All", "durationFilter": "Duration Filter"}, "SHIPMENT": {"objName": "Shipment", "objNames": "Shipments", "itemDescription": "Item Description", "freight": "Freight", "fuelCharges": "Fuel Charges", "Detail": "Shipment Detail", "ADD_NEW_SHIPMENT": "Add New Shipment", "isDeleted": "Deleted?", "customer": "Customer", "shipmentDate": "Shipment Date", "shipmentType": "Shipment Type", "STATUS_DRIVER_ASSIGNED": "DRIVER_ASSIGNED", "STATUS_IN_PROGRESS": "IN_PROGRESS", "STATUS_DELIVERED": "DELIVERED", "status": "Status", "paymentStatus": "Payment Status", "paymentType": "Payment Type", "vehicle": "Vehicle", "barcode": "Barcode", "specialTotal": "Special Total", "grandTotal": "Grand Total", "fuelLevy": "Fuel Levy", "podAdded": "POD Added", "podNotes": "POD Notes", "podSignatures": "Pod Signatures", "podImages": "Pod Images", "messagePodImagesInvalid": "Please Upload At least 1 Pod Images", "driverUser": "DriverUser", "shipmentStatus": "Shipment Status", "ETD": "ETD", "invoice": "Invoice", "referenceID": "Reference/Load ID", "shipmentSecuredProperly": "Shipment Secured <PERSON><PERSON><PERSON>", "assigned": "Assigned", "delayOnLoading": "Delay on Loading", "freightRate": "Freight Rate", "TotalAmount": "Total Amount", "weightType": "Weight Type", "weightLbs": "Weight LBS", "oversize": "Oversize", "startDate": "Shipment Start Date", "endDate": "Shipment End Date", "startTime": "Shipment Start Time", "endTime": "Shipment End Time", "shipmentSummary": "Shipment Summary", "total": "Total", "RushedRateImmediately": "Rushed Rate Immediately", "quantity": "Quantity", "totalItems": "Total Items", "totalWeight": "Total Weight LBS", "totalVolume": "Total Volume", "subTotal": "Sub Total", "gstTotal": "Total GST", "SpecialChargesTotal": "Special Charges Total", "uploaderPodImages": "Upload POD Images", "shipmentCalender": "Shipment Calendar", "PrintStatus": "Print Status", "numberOfUnits": "Number of Units"}, "Vehicle": {"objName": "Vehicle", "objNames": "Vehicles", "Detail": "Vehicle Detail", "ADD_NEW_VEHICLE": "Add New Vehicle", "VehicleInfo": "Vehicle Information"}, "ShipmentItem": {"objName": "Shipment Item", "objNames": "Shipment Items", "Detail": "Shipment Item Detail", "ADD_NEW_SHIPMENTITEM": "Add New Shipment Item", "shipment": "Shipment", "description": "Description", "WEIGHTTYPE_POUNDS": "POUNDS", "WEIGHTTYPE_KGS": "KGS", "weightType": "Weight Type", "weightInPounds": "Weight In Pounds", "RATETYPE_WEIGHT": "WEIGHT", "RATETYPE_VOLUME": "VOLUME", "RATETYPE_HALF_SKID": "HALF_SKID", "RATETYPE_FULL_SKID": "FULL_SKID"}, "FuelReceipt": {"objName": "Fuel Receipt", "objNames": "Fuel Receipts", "Detail": "Fuel Receipt Detail", "ADD_NEW_FUELRECEIPT": "Add New Fuel Receipt", "vehicle": "Vehicle Number", "driverName": "Driver Name", "meterReading": "Meter Reading", "fuelInLiters": "Fuel", "fuelCost": "Fuel Cost", "FUELTYPE_GASOLINE": "GASOLINE", "FUELTYPE_DIESEL": "DIESEL", "FUELTYPE_ELECTRIC": "ELECTRIC", "FUELTYPE_PROPANE": "PROPANE", "FUELTYPE_NATURALGAS": "NATURALGAS", "FUELTYPE_ETHANOL": "ETHANOL", "FUELTYPE_FLEXFUEL": "FLEXFUEL", "FUELTYPE_HYDROGEN": "HYDROGEN", "fuelType": "Fuel Type"}, "Uom": {"objName": "UOM", "objNames": "UOM", "Detail": "UOM Detail", "ADD_NEW_UOM": "Add New UOM", "description": "Description"}, "Address": {"objName": "Address", "objNames": "Addresses", "Detail": "Address Detail", "ADD_NEW_ADDRESS": "Add New Address", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "city": "City", "state": "State", "country": "Country", "province": "Province", "pin": "<PERSON>n", "landmark": "Landmark", "phoneNumber": "Phone Number", "TYPE_HOME": "HOME", "TYPE_OFFICE": "OFFICE", "type": "Type", "latitude": "Latitude", "longitude": "Longitude", "gpsCoordinates": "Gps Coordinates", "entityName": "Entity Name", "entityPkId": "EntityPkId", "unitNumber": "Unit Number", "postalCode": "Postal Code"}, "Notes": {"objName": "Notes", "objNames": "Notes", "Detail": "Notes Detail", "ADD_NEW_NOTES": "Add New Notes", "notes": "Notes", "entityName": "EntityName", "entityPkId": "EntityPkId"}, "DriverLocation": {"objName": "Driver Location", "objNames": "Driver Locations", "Detail": "Driver Location Detail", "ADD_NEW_DRIVERLOCATION": "Add New Driver Location", "driver": "Driver", "latitude": "Latitude", "longitude": "Longitude"}, "Notification": {"objName": "Notification", "objNames": "Notifications", "Detail": "Notification Detail", "ADD_NEW_NOTIFICATION": "Add New Notification", "TYPE_BELL": "BELL", "TYPE_PUSH_NOTIFICATION": "PUSH_NOTIFICATION", "TYPE_EMAIL": "EMAIL", "TYPE_WHATSAPP": "WHATSAPP", "TIMELINE": "Time line", "type": "Type", "title": "Title", "message": "Message", "entityName": "Entity Name", "entityPkId": "EntityPkId", "device": "<PERSON><PERSON>", "isRead": "IsRead", "viewAllNotifications": "View All Notifications", "ReadAll": "Read All", "NoNotificationsAvailable": "No Notifications Available", "clearAll": "Clear All"}, "NotificationUserSetting": {"objName": "NotificationUserSetting", "objNames": "NotificationUserSettings", "Detail": "NotificationUserSetting Detail", "ADD_NEW_NOTIFICATIONUSERSETTING": "Add New NotificationUserSetting", "type": "Type", "key": "Key", "isEnable": "IsEnable", "title": "Title"}, "NotificationSetting": {"objName": "NotificationSetting", "objNames": "NotificationSettings", "Detail": "NotificationSetting Detail", "ADD_NEW_NOTIFICATIONSETTING": "Add New NotificationSetting", "type": "Type", "title": "Title", "content": "Content", "isEnable": "IsEnable", "roleId": "RoleId", "key": "Key"}, "ModuleMaster": {"objName": "ModuleMaster", "objNames": "ModuleMaster", "Detail": "ModuleMaster Detail", "ADD_NEW_MODULEMASTER": "Add New ModuleMaster"}, "RolePermission": {"objName": "RolePermission", "objNames": "RolePermissions", "Detail": "RolePermission Detail", "ADD_NEW_ROLEPERMISSION": "Add New RolePermission", "module": "<PERSON><PERSON><PERSON>", "add": "Add", "edit": "Edit", "remove": "Remove", "export": "Export"}, "UserDevice": {"objName": "User Device", "objNames": "UserDevices", "Detail": "User <PERSON>ce Detail", "ADD_NEW_USERDEVICE": "Add New User Device", "token": "Token", "uuid": "<PERSON><PERSON>"}, "UserPermission": {"objName": "UserPermission", "objNames": "UserPermissions", "Detail": "UserPermission Detail", "ADD_NEW_USERPERMISSION": "Add New UserPermission", "module": "<PERSON><PERSON><PERSON>", "add": "Add", "edit": "Edit", "remove": "Remove", "export": "Export"}, "UserProfile": {"objName": "UserProfile", "objNames": "UserProfile", "Detail": "UserProfile Detail", "ADD_NEW_USERPROFILE": "Add New UserProfile", "passportNumber": "Passport Number", "emergencyContactNumber": "Emergency Contact Number", "emergencyContactName": "Emergency Contact Name", "driverNumber": "Driver Number"}, "Driver": {"objName": "Driver", "objNames": "Drivers", "Detail": "Driver Detail", "ADD_NEW_DRIVER": "Add New Driver", "driverCode": "Driver Code", "doj": "Date Of Joining"}, "NotificationConfiguration": {"objName": "NotificationConfiguration", "objNames": "NotificationConfiguration", "Detail": "NotificationConfiguration Detail", "ADD_NEW_NOTIFICATIONCONFIGURATION": "Add New NotificationConfiguration", "emailSupported": "EmailSupported", "emailEnabled": "EmailEnabled", "bellSupported": "BellSupported", "bellEnabled": "BellEnabled", "pushSupported": "<PERSON><PERSON> Supported", "pushEnabled": "Push Enabled", "cofigurationKey": "CofigurationKey", "subject": "Subject", "body": "Body"}, "ContactUs": {"objName": "ContactUs", "objNames": "ContactUs", "Detail": "ContactUs Detail", "ADD_NEW_CONTACTUS": "Add New ContactUs", "phone": "Phone", "subject": "Subject", "message": "Message", "isResponded": "IsResponded", "responseMessage": "Response Message", "respondedOn": "Responded On", "iPAddress": "<PERSON><PERSON><PERSON>", "STATUS_NEW": "NEW", "STATUS_IN_PROGRESS": "IN_PROGRESS", "STATUS_RESOLVED": "RESOLVED", "status": "Status"}, "CustomerAddress": {"objName": "Customer<PERSON><PERSON><PERSON>", "objNames": "CustomerAddresses", "Detail": "Customer<PERSON><PERSON><PERSON> Detail", "ADD_NEW_CUSTOMERADDRESS": "Add New CustomerAddress", "id": "id", "tenantId": "Tenant", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isActive": "Active?", "customer": "Customer", "TYPE_HOME": "HOME", "TYPE_OFFICE": "OFFICE", "TYPE_OTHER": "OTHER", "type": "Type", "isDefault": "isDefault"}, "Contacts": {"objName": "Contacts", "objNames": "Contact Us", "personName": "Person Name", "contact": "Contact No.", "email": "Email", "message": "Message", "subject": "Subject", "status": "Status", "changeStatus": "Update Status"}}
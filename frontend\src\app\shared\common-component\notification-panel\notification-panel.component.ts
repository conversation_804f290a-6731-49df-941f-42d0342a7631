import { Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { NotificationsService } from '../../../pages/layout/notifications/notifications.service';

@Component({
    selector: 'app-notification-panel',
    standalone: true,
    imports: [RouterLink, TranslateModule],
    templateUrl: './notification-panel.component.html',
    styleUrl: './notification-panel.component.scss',
})
export class NotificationPanelComponent {
    count = 0;
    @Input() groupedNotifications: { label: string; items: any[] }[] = [];

    private countSubscription?: Subscription;
    constructor(private notificationsService: NotificationsService) { }
    ngOnInit(): void {
        // Subscribe to count changes
        this.countSubscription = this.notificationsService.count$.subscribe(count => {
            console.log('Notification count updated:', count);
            this.count = count;
        });
    }

    ngOnD<PERSON>roy(): void {
        this.countSubscription?.unsubscribe();
    }
}
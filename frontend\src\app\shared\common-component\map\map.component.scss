/* ===== GOOGLE MAPS INFO WINDOW OVERRIDES ===== */
::ng-deep .gm-style-iw {
    padding: 0 !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
    border: none !important;
    max-width: 320px !important;
}

::ng-deep .gm-style-iw-d {
    overflow: hidden !important;
    max-height: none !important;
}

::ng-deep .gm-style-iw-t::after {
    display: none !important;
}

::ng-deep .gm-style-iw-c {
    padding: 0 !important;
    border-radius: 12px !important;
}

/* Remove Google's close button - we'll add our own if needed */
::ng-deep .gm-style-iw button {
    display: none !important;
}

/* ===== INFO WINDOW CONTENT STYLES ===== */
.info-window-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    min-width: 280px;
    max-width: 320px;
}

.info-window-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    z-index: -1;
}

/* ===== HEADER STYLES ===== */
.info-header {
    background: rgba(255, 255, 255, 0.1);
    padding: 16px 20px 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.info-header .icon {
    font-size: 24px;
    margin-right: 8px;
    vertical-align: middle;
}

.info-header strong {
    font-size: 18px;
    font-weight: 600;
    letter-spacing: -0.025em;
}

/* ===== DETAILS GRID ===== */
.details-grid {
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    margin: 0;
}

.details-grid .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    transition: background-color 0.2s ease;
}

.details-grid .detail-row:last-child {
    border-bottom: none;
}

.details-grid .detail-row:hover {
    background: rgba(0, 0, 0, 0.02);
    margin: 0 -20px;
    padding-left: 20px;
    padding-right: 20px;
}

.details-grid .detail-label {
    font-weight: 600;
    color: #555;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 80px;
}

.details-grid .detail-value {
    font-weight: 500;
    color: #333;
    font-size: 14px;
    text-align: right;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
}

/* ===== ADDRESS SECTION ===== */
.address-section {
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.9);
    border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.address-section strong {
    color: #555;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.address-section small {
    color: #666;
    font-size: 12px;
    line-height: 1.4;
    display: block;
    margin-top: 4px;
}

/* ===== ACTION BUTTONS ===== */
.action-buttons {
    padding: 16px 20px 20px;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    gap: 8px;
    justify-content: center;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border-radius: 8px;
    text-decoration: none;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    border: none;
    transition: all 0.2s ease;
    flex: 1;
    justify-content: center;
    max-width: 120px;
}

.btn-outline-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-outline-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.btn-outline-success {
    background: linear-gradient(135deg, #11998e, #38ef7d);
    color: white;
    box-shadow: 0 4px 12px rgba(56, 239, 125, 0.4);
}

.btn-outline-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(56, 239, 125, 0.6);
}

.btn:active {
    transform: translateY(0);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 480px) {
    .info-window-content {
        min-width: 260px;
        max-width: 300px;
    }

    .details-grid,
    .address-section,
    .action-buttons {
        padding-left: 16px;
        padding-right: 16px;
    }

    .info-header {
        padding-left: 16px;
        padding-right: 16px;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center {
    text-align: center;
}

.mb-1 {
    margin-bottom: 4px;
}

.mb-2 {
    margin-bottom: 8px;
}

.mt-2 {
    margin-top: 8px;
}

.p-2 {
    padding: 8px;
}

.p-3 {
    padding: 12px;
}

.d-flex {
    display: flex;
}

.gap-1 {
    gap: 4px;
}

/* ===== LOADING ANIMATION ===== */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===== STATUS INDICATORS ===== */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-active {
    background: #10b981;
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.status-inactive {
    background: #ef4444;
}

.status-pending {
    background: #f59e0b;
    animation: pulse-status 2s infinite;
}

@keyframes pulse-status {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {

    .details-grid,
    .address-section,
    .action-buttons {
        background: rgba(31, 41, 55, 0.95);
        color: #f9fafb;
    }

    .details-grid .detail-label {
        color: #d1d5db;
    }

    .details-grid .detail-value {
        color: #f3f4f6;
    }

    .address-section strong {
        color: #d1d5db;
    }

    .address-section small {
        color: #9ca3af;
    }

    .details-grid .detail-row:hover {
        background: rgba(55, 65, 81, 0.3);
    }
}
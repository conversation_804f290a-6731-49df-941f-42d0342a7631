<div class="action-icons bell-container">
    <button class="edit-btn bell-icon-button" [ngbPopover]="popContent" #popoverRef="ngbPopover"
        popoverClass="notification-option-popup custom-popover" (shown)="onNotificationPanelOpen()" [animation]="false"
        [autoClose]="'outside'" placement="bottom-end">
        <i class="bi bi-bell bell-icon"></i>
        <span *ngIf="notificationCount > 0" class="notification-badge">
            {{ notificationCount > 99 ? '99+' : notificationCount }}
        </span>
    </button>

    <ng-template #popContent>
        <div class="d-flex justify-content-center">
            <div class="notification-panel d-flex flex-column justify-content-between">
                <ng-container *ngIf="!isNotificationLoading">
                    <div>
                        <div class="header p-1">
                            <h3>{{ 'Notification.objNames' | translate }}</h3>
                            <span class="new-badge fw-500" *ngIf="(count$ | async) as count">
                                {{ count }} New
                            </span>
                            <i class="icon-envelope"></i>
                        </div>
                        <div class="line-1 mb-2"></div>
                    </div>

                    <div class="scrolling-section"
                        [ngClass]="{ 'scrolling-section-length-zero': notifications.length === 0 }"
                        (scroll)="onScroll($event)">
                        <div class="notification-list">
                            <!-- ✅ Show grouped notifications if there are any -->
                            <ng-container *ngIf="notifications.length > 0; else notificationListEmpty">
                                <ng-container *ngFor="let group of groupedNotifications">
                                    <div class="notification-group" *ngIf="group.items.length > 0">
                                        <div class="sticky-header mb-2">{{ group.label }}</div>

                                        <div class="notification-item" *ngFor="let notification of group.items"
                                            (click)="redirectToUser(notification)">
                                            <div class="avatar placeholder">
                                                <i [ngClass]="notification.iconClass" class="font-size-18"></i>
                                            </div>
                                            <div class="content" appRippleEffect>
                                                <p class="title ellipsis-2-line"
                                                    [appTooltipEllipsis]="notification.message" [ngbTooltip]
                                                    container="body" placement="auto">
                                                    {{ notification.message }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </ng-container>
                            </ng-container>
                        </div>
                    </div>

                    <!-- ✅ Empty panel shown only when notifications.length === 0 -->
                    <ng-template #notificationListEmpty>
                        <div class="no-data-wrapper mx-md-2 mt-2 h-100">
                            <div class="no-data-box">
                                <img src="/assets/images/svg/no-data.svg" alt="No Data" />
                                <p class="text-center mt-2 fw-600 font-size-16">
                                    {{ 'Notification.NoNotificationsAvailable' | translate }}
                                </p>
                            </div>
                        </div>
                    </ng-template>

                    <div>
                        <button class="view-all" appRippleEffect [routerLink]="['/dashboard/notifications']">
                            {{ 'Notification.viewAllNotifications' | translate }}
                        </button>
                    </div>
                </ng-container>

                <div *ngIf="isNotificationLoading" class="d-flex justify-content-center align-items-center h-100">
                    <span class="loader"></span>
                </div>
            </div>
        </div>
    </ng-template>
</div>
import { CommonModule } from '@angular/common';
import {
    AfterViewInit, Component, Input, NgZone,
    OnChanges, OnDestroy, SimpleChanges, ViewChild
} from '@angular/core';
import {
    GoogleMap, GoogleMapsModule, MapCircle,
    MapInfoWindow, MapMarker
} from '@angular/google-maps';
import { loadGoogleMaps } from '../../../utils/mapLoader';

declare const google: any;

// Types
type LatLngLike = { lat: number | string; lng: number | string };
type Driver = { lat: number | string; lng: number | string; name: string };

@Component({
    selector: 'app-map',
    standalone: true,
    imports: [GoogleMap, MapInfoWindow, MapCircle, MapMarker, CommonModule, GoogleMapsModule],
    templateUrl: './map.component.html',
})
export class MapComponent implements AfterViewInit, OnChanges, OnDestroy {

    // ViewChild References - FIXED: Use single info window
    @ViewChild(GoogleMap) map?: GoogleMap;
    @ViewChild(MapInfoWindow) infoWindow?: MapInfoWindow; // Single info window

    // Input Properties
    @Input() apiKey = '';
    @Input() drivers: Driver[] = [];
    @Input() centerPoint!: LatLngLike;
    @Input() shipment = false;

    // State Properties
    isMapsReady = false;
    destroyed = false;
    centerAddress: string = '';
    selectedInfoType: 'center' | 'driver' | null = null;

    // Location Properties
    defaultCenter: google.maps.LatLngLiteral = { lat: 51.0447, lng: -114.0719 };
    center: google.maps.LatLngLiteral = { lat: 0, lng: 0 };
    visibleDrivers: { lat: number; lng: number; name: string }[] = [];
    selectedLocation: any = {};

    // Map Configuration
    initialZoom = 12;
    radiusInMeters = 200;

    mapOptions: google.maps.MapOptions = {
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
        zoomControl: true,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
    };

    // Icon Configuration
    carIcon: google.maps.Icon = {
        url: '/assets/images/delivery-truck.svg',
        scaledSize: new google.maps.Size(40, 40),
    };

    centerIcon: google.maps.Icon = {
        url: 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png',
        scaledSize: new google.maps.Size(40, 40),
    };

    // Circle Configuration
    circleOptions: google.maps.CircleOptions = {
        fillColor: '#FF0000',
        fillOpacity: 0.2,
        strokeColor: '#FF0000',
        strokeOpacity: 0.5,
        strokeWeight: 2,
        clickable: false,
        editable: false,
        draggable: false,
        zIndex: 1,
    };

    constructor(private ngZone: NgZone) { }

    // Lifecycle Methods
    ngAfterViewInit() {
        this.initGoogleMaps();
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['centerPoint'] && this.centerPoint) {
            this.center = this.normalizeLatLng(this.centerPoint) || this.defaultCenter;
            if (this.isMapsReady) {
                setTimeout(() => this.fitMapAndReverseGeocode(), 50);
            }
        }
        if (changes['drivers']) {
            this.visibleDrivers = this.normalizeDrivers(this.drivers);
            if (this.isMapsReady) {
                setTimeout(() => this.fitMapAndReverseGeocode(), 50);
            }
        }
    }

    ngOnDestroy(): void {
        this.destroyed = true;
    }

    // Map Initialization
    private async initGoogleMaps(): Promise<void> {
        if (!(window as any).google?.maps) {
            if (!this.apiKey) {
                console.warn('Google Maps not loaded and no apiKey provided.');
                return;
            }
            try {
                await loadGoogleMaps(this.apiKey);
            } catch (e) {
                console.error('Failed to load Google Maps:', e);
                return;
            }
        }

        // Wait for Google Maps to be fully initialized
        setTimeout(() => {
            if (this.destroyed) return;
            this.isMapsReady = true;

            this.center = this.normalizeLatLng(this.centerPoint) || this.defaultCenter;
            this.visibleDrivers = this.normalizeDrivers(this.drivers);

            setTimeout(() => {
                if (!this.destroyed) {
                    this.fitMapAndReverseGeocode();
                }
            }, 100);
        }, 100);
    }

    // Map Fitting and Bounds
    private fitMapAndReverseGeocode(): void {
        const mapInstance = this.map?.googleMap;
        if (!mapInstance || !this.isMapsReady) return;

        const bounds = new google.maps.LatLngBounds();
        let hasPoints = false;

        if (this.center.lat !== 0 && this.center.lng !== 0) {
            bounds.extend(this.center);
            hasPoints = true;
        }

        if (this.visibleDrivers.length > 0) {
            this.visibleDrivers.forEach(d => {
                bounds.extend(new google.maps.LatLng(d.lat, d.lng));
                hasPoints = true;
            });
        }

        if (hasPoints && !bounds.isEmpty()) {
            mapInstance.fitBounds(bounds);

            google.maps.event.addListenerOnce(mapInstance, 'bounds_changed', () => {
                let currentZoom = mapInstance.getZoom() ?? this.initialZoom;
                const maxZoom = 16;
                const minZoom = 8;
                if (currentZoom > maxZoom) currentZoom = maxZoom;
                if (currentZoom < minZoom) currentZoom = minZoom;
                mapInstance.setZoom(currentZoom);
            });
        } else {
            mapInstance.setCenter(this.defaultCenter);
            mapInstance.setZoom(this.initialZoom);
        }

        this.getCenterAddress();
    }

    // Data Normalization
    private toNumber(v: any): number {
        const n = typeof v === 'string' ? parseFloat(v) : v;
        return isNaN(n) ? 0 : n;
    }

    private normalizeLatLng(input: LatLngLike): google.maps.LatLngLiteral | null {
        if (!input) return null;
        let lat = this.toNumber(input.lat);
        let lng = this.toNumber(input.lng);

        if (Math.abs(lat) > 90 && Math.abs(lng) <= 90) [lat, lng] = [lng, lat];
        if (lat === 0 && lng === 0) return null;

        return { lat, lng };
    }

    private normalizeDrivers(drivers: Driver[]) {
        return (drivers || []).map(d => ({
            lat: this.toNumber(d.lat),
            lng: this.toNumber(d.lng),
            name: d.name,
        })).filter(d => d.lat !== 0 && d.lng !== 0);
    }

    // Distance Calculation
    private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
        const R = 6371; // Radius of Earth in kilometers
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLng / 2) * Math.sin(dLng / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c * 1000; // Return distance in meters
    }

    // In your click handlers, change this:

    // FIXED: Now accepts MapMarker type parameter
    onCenterMarkerClick(marker: MapMarker) {
        this.selectedInfoType = 'center';

        if (!this.centerAddress) {
            this.getCenterAddress();
        }

        setTimeout(() => {
            if (this.infoWindow) {
                this.infoWindow.open(marker);
            }
        }, 100);
    }

    // FIXED: Now accepts MapMarker type parameter
    onDriverMarkerClick(marker: MapMarker, index: number) {
        this.selectedInfoType = 'driver';
        const driver = this.visibleDrivers[index];

        if (driver) {
            this.selectedLocation = {
                name: driver.name,
                lat: driver.lat,
                lng: driver.lng,
                distanceFromCenter: this.calculateDistance(
                    this.center.lat, this.center.lng,
                    driver.lat, driver.lng
                ),
                address: ''
            };

            this.getDriverAddress(driver.lat, driver.lng);
        }

        setTimeout(() => {
            if (this.infoWindow) {
                this.infoWindow.open(marker);
            }
        }, 100);
    }


    onCenterMarkerClickWithEvent(marker: MapMarker) {
        this.selectedInfoType = 'center';

        if (!this.centerAddress) {
            this.getCenterAddress();
        }

        setTimeout(() => {
            if (this.infoWindow) {
                this.infoWindow.open(marker);
            }
        }, 100);
    }

    onDriverMarkerClickWithEvent(marker: MapMarker, index: number) {
        this.selectedInfoType = 'driver';
        const driver = this.visibleDrivers[index];

        if (driver) {
            this.selectedLocation = {
                name: driver.name,
                lat: driver.lat,
                lng: driver.lng,
                distanceFromCenter: this.calculateDistance(
                    this.center.lat, this.center.lng,
                    driver.lat, driver.lng
                ),
                address: ''
            };

            this.getDriverAddress(driver.lat, driver.lng);
        }

        setTimeout(() => {
            if (this.infoWindow) {
                this.infoWindow.open(marker);
            }
        }, 100);
    }

    public refreshView(): void {
        const mapInstance = this.map?.googleMap;
        if (!mapInstance || !this.isMapsReady) return;

        // Force relayout
        google.maps.event.trigger(mapInstance, 'resize');

        // Re-apply center or bounds
        if (this.visibleDrivers?.length) {
            this.fitMapAndReverseGeocode();
        } else if (this.center?.lat && this.center?.lng) {
            mapInstance.setCenter(this.center);
            if (this.initialZoom) mapInstance.setZoom(this.initialZoom);
        }
    }

    // Geocoding Methods
    private async getCenterAddress(): Promise<void> {
        if (!google?.maps?.Geocoder) return;

        const geocoder = new google.maps.Geocoder();
        try {
            const result = await geocoder.geocode({ location: this.center });
            if (result.results && result.results.length > 0) {
                this.centerAddress = result.results[0].formatted_address;
            }
        } catch (error) {
            console.error('Geocoding failed:', error);
        }
    }

    private async getDriverAddress(lat: number, lng: number): Promise<void> {
        if (!google?.maps?.Geocoder) return;

        const geocoder = new google.maps.Geocoder();
        try {
            const result = await geocoder.geocode({ location: { lat, lng } });
            if (result.results && result.results.length > 0) {
                this.selectedLocation.address = result.results[0].formatted_address;
            }
        } catch (error) {
            console.error('Driver geocoding failed:', error);
        }
    }

    // Utility Methods
    copyCoordinates(): void {
        const coords = `${this.center.lat}, ${this.center.lng}`;
        navigator.clipboard.writeText(coords).then(() => {
            alert('Center coordinates copied to clipboard!');
        }).catch(err => {
            console.error('Failed to copy coordinates:', err);
        });
    }

    copyDriverCoordinates(): void {
        const coords = `${this.selectedLocation.lat}, ${this.selectedLocation.lng}`;
        navigator.clipboard.writeText(coords).then(() => {
            alert('Driver coordinates copied to clipboard!');
        }).catch(err => {
            console.error('Failed to copy coordinates:', err);
        });
    }

    openInGoogleMaps(): void {
        const url = `https://www.google.com/maps?q=${this.center.lat},${this.center.lng}`;
        window.open(url, '_blank');
    }

    openDriverInGoogleMaps(): void {
        const url = `https://www.google.com/maps?q=${this.selectedLocation.lat},${this.selectedLocation.lng}`;
        window.open(url, '_blank');
    }

    // Track by function for ngFor
    trackDriver = (_: number, d: { lat: number; lng: number; name: string }) =>
        `${d.lat},${d.lng},${d.name}`;
}

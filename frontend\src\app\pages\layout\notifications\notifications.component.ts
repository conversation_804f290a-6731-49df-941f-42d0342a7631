// Angular Core Modules
import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// Third-party Modules
import { NgbAccordionModule, NgbPopoverModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { DataTablesModule } from 'angular-datatables';
import moment from 'moment';
import { Observable } from 'rxjs';

// Application Components, Directives, Pipes and Services
import { LoadingService } from '../../../services/loading.service';
import { ToastService } from '../../../shared/services/toast.service';
import { TypeAheadService } from '../../../shared/services/typeahead-search.service';

// Application Configurations and Models
import { Constant } from '../../../config/constants';
import { FilterParam } from '../../../models/common/filter-param';
import { Notification } from '../../../models/notification';
import { NotificationsManager } from './notifications.manager';
import { NotificationsService } from './notifications.service';

@Component({
    selector: 'app-notifications',
    standalone: true,
    imports: [
        FormsModule,
        CommonModule,
        DataTablesModule,
        NgbAccordionModule,
        NgbTooltipModule,
        NgSelectModule,
        TranslateModule,
        NgbPopoverModule,
    ],
    templateUrl: './notifications.component.html',
    styleUrl: './notifications.component.scss',
})
export class NotificationsComponent implements OnInit, OnDestroy, AfterViewInit {
    @ViewChild('scrollContainer') scrollContainer!: ElementRef;
    @ViewChild('sentinel') sentinel!: ElementRef;

    private observer!: IntersectionObserver;
    private isFirstLoad = true;

    notifications: Notification[] = [];
    groupedNotifications: { label: string; items: Notification[] }[] = [];

    currentPage = 1;
    pageSize = 20;
    isLoadingMore = false;
    isLastPage = false;

    filterParam = new FilterParam();

    isAllSelected = false;
    hasUnread = false;

    isLoaded: boolean = false;

    resourceType: string = Constant.RESOURCE_TYPE.RATE_SHEET;

    constructor(
        protected notificationsManager: NotificationsManager,
        protected route: ActivatedRoute,
        protected typeAheadService: TypeAheadService,
        protected notificationsService: NotificationsService,
        protected router: Router,
        protected loadingService: LoadingService,
        protected toastService: ToastService
    ) {
    }

    ngOnInit(): void {
        this.fetchNotifications();
        this.loadNotificationCount();
    }

    ngAfterViewInit(): void {
        if (this.scrollContainer?.nativeElement && this.sentinel?.nativeElement) {
            this.initIntersectionObserver();
        }
    }

    ngOnDestroy(): void {
        if (this.observer) {
            this.observer.disconnect();

        }
    }

    clearAll(): void {
        this.notificationsManager.notificationActions(true).subscribe({
            next: (res) => {
                this.toastService.success(res?.message);
                this.resetNotifications();
                this.fetchNotifications();
            },
        });
    }

    private loadNotificationCount(): void {
        const filterParam = new FilterParam();
        this.notificationsManager.fetchNotificationCount(filterParam).subscribe({
            next: (response) => {
                const count = response.data?.totalCount ?? 0;
                this.notificationsService.setCount(count);
            },
            error: (error) => {
                console.error('Failed to load notification count', error);
            }
        });
    }


    deleteNotification(item: Notification, event?: MouseEvent): void {
        event?.stopPropagation(); // prevent card click -> redirect
        const action$ = this.notificationsManager.notificationAction([{ id: item.id }], true);
        this.performNotificationAction(action$, () =>
            this.removeNotificationLocally(item.id)
        );
    }

    formatMessage(text: string = ''): string {
        return text ? text.replace(/'([^']+)'/g, '<strong>$1</strong>') : '';
    }
    markAsRead(item: Notification, event?: MouseEvent): void {
        event?.stopPropagation();

        const action$ = this.notificationsManager.notificationAction([{ id: item.id }]);

        this.performNotificationAction(action$, () => {
            this.updateNotificationLocally(item.id, { isRead: true });
            // Only decrease count if the notification was unread
            if (!item.isRead) {
                this.notificationsService.decreaseCount();
            }
        });
    }



    markAllAsRead(): void {

        this.notificationsManager.notificationActions(false).subscribe({
            next: (res) => {
                this.toastService.success(res?.message);

                // ✅ Just update locally instead of reloading from API
                this.groupedNotifications.forEach(group => {
                    group.items.forEach(item => (item.isRead = true));
                });
            },
        });
    }

    redirectToUser(record: any) {
        if (!record.isRead) {
            this.markAsRead(record.id);
        }

        const routeMappings: any = {
            CUSTOMER: 'dashboard/customer',
            QUOTATION: 'dashboard/quotation',
            RATESHEET: 'dashboard/rate-sheet',
            SHIPMENT: 'dashboard/shipment',
            BARCODE: 'dashboard/barcodes'
        };

        const route = routeMappings[record.entityName] ?? '/dashboard/notifications';

        // Optional: Add state only for certain types
        let state: any = undefined;
        if (['SHIPMENT', 'QUOTATION'].includes(record.entityName)) {
            state = {
                step: 1,
                firstLoad: true
            };
        }

        this.reDirectBasedOnStatus(route, record?.entityPkId, state);
    }

    protected getIconClass(entityName: string): string {
        const basePath = '/assets/images/svg/menu/';
        const key = (entityName || '').toUpperCase();

        if (key.includes('SHIPMENT')) return `${basePath}delivery-truck.svg`;
        if (key.includes('QUOTATION')) return `${basePath}app.svg`;
        if (key.includes('BARCODE')) return `${basePath}barcode.svg`;
        if (key.includes('CUSTOMER')) return `${basePath}customers.svg`;
        if (key.includes('RATESHEET')) return `${basePath}price-list.svg`;

        return `${basePath}bell.svg`;
    }

    protected onScroll(event: any): void {
        const { scrollTop, scrollHeight, clientHeight } = event.target;
        const threshold = 100;
        if (scrollHeight - scrollTop <= clientHeight + threshold) {
            this.fetchNotifications();
        }
    }

    get hasUnreadNotifications(): boolean {
        return this.groupedNotifications?.some(group =>
            group.items?.some(item => !item.isRead)
        );
    }

    get hasNonDeletedNotifications(): boolean {
        return this.groupedNotifications?.some(group =>
            group.items?.some(item => !item.isDeleted)
        );
    }

    private initIntersectionObserver(): void {
        if (!this.scrollContainer?.nativeElement || !this.sentinel?.nativeElement) {
            return; // don't init if not rendered yet
        }

        this.observer = new IntersectionObserver(
            (entries) => {
                if (
                    entries.some((entry) => entry.isIntersecting) &&
                    !this.isLoadingMore &&
                    !this.isLastPage
                ) {
                    this.fetchNotifications();
                }
            },
            {
                root: this.scrollContainer.nativeElement,
                rootMargin: '50px',
                threshold: 0.1,
            }
        );

        this.observer.observe(this.sentinel.nativeElement);
    }

    private fetchNotifications(isAutoLoad: boolean = false): void {
        if (this.isLoadingMore || this.isLastPage) return;

        this.isLoadingMore = true;
        this.filterParam.pagination.offset = this.currentPage;
        this.filterParam.pagination.next = this.pageSize;

        this.notificationsManager.getNotifications(this.filterParam).subscribe({
            next: (response: any) => {
                const newData = response.data.map((x: any) =>
                    Notification.fromResponse(x)
                );

                this.notifications.push(...newData);
                this.groupedNotifications = this.groupNotificationsByDate(this.notifications);

                this.isLastPage = newData.length < this.pageSize;
                this.currentPage++;
                this.isLoadingMore = false;

                this.isLoaded = true;

                if (isAutoLoad || this.isFirstLoad) {
                    this.isFirstLoad = false;
                    setTimeout(() => this.loadIfNotFull(), 100);
                }
            },
            error: () => {
                this.isLoadingMore = false;
                this.isLoaded = true;
            },
        });
    }

    private loadIfNotFull(): void {
        const container = this.scrollContainer.nativeElement;

        // If items don't fill the viewport, keep loading
        if (
            container.scrollHeight <= container.clientHeight &&
            !this.isLastPage &&
            !this.isLoadingMore
        ) {
            this.fetchNotifications(true);
        }
    }

    private groupNotificationsByDate(notifications: Notification[]) {
        const grouped: { [key: string]: Notification[] } = {};

        for (const notification of notifications) {
            const date = moment(notification.createdOn);
            let groupLabel = '';

            if (date.isSame(moment(), 'day')) {
                groupLabel = 'Today';
            } else if (date.isSame(moment().subtract(1, 'day'), 'day')) {
                groupLabel = 'Yesterday';
            } else if (date.isSame(moment(), 'month') && date.isSame(moment(), 'year')) {
                groupLabel = 'This Month';
            } else if (date.isSame(moment().subtract(1, 'month'), 'month')) {
                groupLabel = moment(date).format('MMMM');
            } else if (date.isSame(moment().subtract(1, 'year'), 'year')) {
                groupLabel = 'Last Year';
            } else {
                groupLabel = moment(date).format('MMMM YYYY');
            }

            if (!grouped[groupLabel]) {
                grouped[groupLabel] = [];
            }

            grouped[groupLabel].push(notification);
        }

        const sortedEntries = Object.entries(grouped).sort(([a], [b]) => {
            const parseDate = (label: string) => {
                switch (label) {
                    case 'Today': return moment();
                    case 'Yesterday': return moment().subtract(1, 'day');
                    case 'This Month': return moment().startOf('month');
                    case 'Last Year': return moment().subtract(1, 'year');
                    default: return moment(label, ['MMMM YYYY', 'MMMM'], true);
                }
            };

            return parseDate(b).valueOf() - parseDate(a).valueOf();
        });

        return sortedEntries.map(([label, items]) => ({
            label,
            items: items.slice().sort((a, b) => {
                const dateA = new Date(a.createdOn).getTime();
                const dateB = new Date(b.createdOn).getTime();
                return dateB - dateA;
            }),
        }));
    }

    private reDirectBasedOnStatus(baseRoute: string, id?: string, state?: any): void {
        const targetRoute = id ? `/${baseRoute}/edit/${id}` : `/${baseRoute}s`;

        if (this.router.url === targetRoute) return;

        this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
            if (state) {
                this.router.navigate([targetRoute], { state });
            } else {
                this.router.navigate([targetRoute]);
            }
        });
    }

    private updateNotificationLocally(id: string, changes: Partial<Notification>) {
        this.groupedNotifications.forEach(group => {
            const notify = group.items.find(item => item.id === id);
            if (notify) Object.assign(notify, changes);
        });
    }

    private removeNotificationLocally(id: string) {
        this.groupedNotifications.forEach(group => {
            group.items = group.items.filter(item => item.id !== id);
        });

        this.groupedNotifications = this.groupedNotifications.filter(groupNotification => groupNotification.items.length > 0);
    }

    private performNotificationAction(action$: Observable<any>, onSuccess: () => void): void {
        this.loadingService.show();

        action$.subscribe({
            next: () => onSuccess(),
            error: (err) => {
                console.error('Notification action failed:', err);
            },
            complete: () => {
                this.loadingService.hide();
            }
        });
    }

    private resetNotifications(): void {
        this.notifications = [];
        this.groupedNotifications = [];
        this.currentPage = 0;
        this.isLastPage = false;
        this.isFirstLoad = true;
    }

}
